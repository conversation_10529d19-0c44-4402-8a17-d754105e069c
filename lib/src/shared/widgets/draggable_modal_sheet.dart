import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A reusable draggable modal sheet that can be pulled up/down.
/// Supports velocity-based gestures and provides smooth animations.
class DraggableModalSheet extends StatefulWidget {
  /// The content to display in the modal.
  final Widget child;

  /// The initial height of the modal, from 0.0 to 1.0.
  /// Default is 1.0 (full height).
  final double initialHeight;

  /// The minimum height of the modal, from 0.0 to 1.0.
  /// Default is 0.1.
  final double minHeight;

  /// The velocity threshold for closing the modal.
  /// Default is 300.0.
  final double closeVelocityThreshold;

  /// The height threshold for closing the modal.
  /// Default is 0.3.
  final double closeHeightThreshold;

  /// The height threshold for switching to full height.
  /// Default is 0.75.
  final double fullHeightThreshold;

  /// The mid-height snap point.
  /// Default is 0.5.
  final double midHeight;

  /// Whether to enable snap to mid height.
  /// Default is false.
  final bool snapToMid;

  /// The animation duration for height changes.
  /// Default is 200 milliseconds.
  final Duration animationDuration;

  /// The curve to use for animations.
  /// Default is Curves.easeOutCubic.
  final Curve animationCurve;

  /// Function to call when the modal is closed by dragging.
  final VoidCallback? onClose;

  /// The height of the drag handle area.
  /// Default is 48.0 logical pixels.
  final double dragHandleHeight;

  /// The color of the drag handle.
  /// Default is Color(0xFFDEDEDE).
  final Color dragHandleColor;

  /// Whether to enable toggle behavior when tapping the drag handle.
  /// When true, tapping the handle will toggle between full height and mid height.
  /// Default is false.
  final bool enableDragHandleTap;

  const DraggableModalSheet({
    super.key,
    required this.child,
    this.initialHeight = 1.0,
    this.minHeight = 0.1,
    this.closeVelocityThreshold = 300.0,
    this.closeHeightThreshold = 0.3,
    this.fullHeightThreshold = 0.75,
    this.midHeight = 0.5,
    this.snapToMid = false,
    this.animationDuration = const Duration(milliseconds: 200),
    this.animationCurve = Curves.easeOutCubic,
    this.onClose,
    this.dragHandleHeight = 48.0,
    this.dragHandleColor = const Color(0xFFDEDEDE),
    this.enableDragHandleTap = false,
  });

  @override
  State<DraggableModalSheet> createState() => _DraggableModalSheetState();
}

class _DraggableModalSheetState extends State<DraggableModalSheet> {
  late double modalHeight;
  double? _startDragY;
  double? _startDragHeight;

  @override
  void initState() {
    super.initState();
    modalHeight = widget.initialHeight.clamp(widget.minHeight, 1.0);
  }

  void _snapToHeight() {
    if (modalHeight < widget.closeHeightThreshold) {
      // Close if dragged below threshold
      if (widget.onClose != null) {
        widget.onClose!();
      } else {
        Navigator.of(context).pop();
      }
      return;
    }

    if (widget.snapToMid) {
      // When snapToMid is enabled, snap to mid or full height based on where it is now
      if (modalHeight < widget.fullHeightThreshold) {
        modalHeight = widget.midHeight;
      } else {
        modalHeight = 1.0;
      }
    } else {
      // Traditional behavior: snap to closest point
      final midPoint = (widget.closeHeightThreshold + widget.fullHeightThreshold) / 2;

      if (modalHeight < midPoint) {
        // Close or mid height
        if (modalHeight < widget.closeHeightThreshold) {
          if (widget.onClose != null) {
            widget.onClose!();
          } else {
            Navigator.of(context).pop();
          }
        } else {
          modalHeight = widget.midHeight;
        }
      } else {
        // Mid or full height
        if (modalHeight < widget.fullHeightThreshold) {
          modalHeight = widget.midHeight;
        } else {
          modalHeight = 1.0;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return GestureDetector(
      onVerticalDragStart: (details) {
        _startDragY = details.globalPosition.dy;
        _startDragHeight = modalHeight;
      },
      onVerticalDragUpdate: (details) {
        if (_startDragY == null || _startDragHeight == null) return;

        final currentY = details.globalPosition.dy;
        final dragDistance = currentY - _startDragY!;
        final heightChange = dragDistance / screenHeight;

        // Make upward drags more responsive (less resistance)
        final adjustedChange = heightChange < 0
            ? heightChange * 1.2 // Increase responsiveness for upward drags
            : heightChange;

        setState(() {
          modalHeight = (_startDragHeight! - adjustedChange).clamp(widget.minHeight, 1.0);
        });
      },
      onVerticalDragEnd: (details) {
        _startDragY = null;
        _startDragHeight = null;

        final velocity = details.primaryVelocity ?? 0;
        setState(() {
          if (velocity > widget.closeVelocityThreshold) {
            // Fast downward swipe - close the modal or go to mid point
            if (widget.snapToMid && modalHeight > widget.midHeight) {
              modalHeight = widget.midHeight;
            } else {
              if (widget.onClose != null) {
                widget.onClose!();
              } else {
                Navigator.of(context).pop();
              }
            }
          } else if (velocity < -widget.closeVelocityThreshold) {
            // Fast upward swipe - expand to full or go to mid point
            if (widget.snapToMid && modalHeight < widget.midHeight) {
              modalHeight = widget.midHeight;
            } else {
              modalHeight = 1.0;
            }
          } else {
            // Based on current position - snap to closest point
            _snapToHeight();
          }
        });
      },
      child: AnimatedContainer(
        duration: widget.animationDuration,
        curve: widget.animationCurve,
        height: screenHeight * modalHeight,
        child: Column(
          children: [
            // Improved drag handle - still looks the same but has better touch area
            GestureDetector(
              behavior: HitTestBehavior.opaque, // Makes the entire area respond to touches
              onTap: widget.enableDragHandleTap
                  ? () {
                      setState(() {
                        modalHeight = modalHeight < 0.9 ? 1.0 : widget.midHeight;
                      });
                    }
                  : null,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 8.r), // Slightly larger touch target
                child: Center(
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: 4.r),
                    width: 72.r,
                    height: 4.r,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(9999.r),
                      color: widget.dragHandleColor,
                    ),
                  ),
                ),
              ),
            ),
            // Content
            Expanded(child: widget.child),
          ],
        ),
      ),
    );
  }
}
