// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserHasPassword _$UserHasPasswordFromJson(Map<String, dynamic> json) {
  return _UserHasPassword.fromJson(json);
}

/// @nodoc
mixin _$UserHasPassword {
  String get username => throw _privateConstructorUsedError;
  bool get hasPassword => throw _privateConstructorUsedError;
  String get profilePictureUrl => throw _privateConstructorUsedError;

  /// Serializes this UserHasPassword to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserHasPassword
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserHasPasswordCopyWith<UserHasPassword> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserHasPasswordCopyWith<$Res> {
  factory $UserHasPasswordCopyWith(
          UserHasPassword value, $Res Function(UserHasPassword) then) =
      _$UserHasPasswordCopyWithImpl<$Res, UserHasPassword>;
  @useResult
  $Res call({String username, bool hasPassword, String profilePictureUrl});
}

/// @nodoc
class _$UserHasPasswordCopyWithImpl<$Res, $Val extends UserHasPassword>
    implements $UserHasPasswordCopyWith<$Res> {
  _$UserHasPasswordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserHasPassword
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? hasPassword = null,
    Object? profilePictureUrl = null,
  }) {
    return _then(_value.copyWith(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      hasPassword: null == hasPassword
          ? _value.hasPassword
          : hasPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      profilePictureUrl: null == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserHasPasswordImplCopyWith<$Res>
    implements $UserHasPasswordCopyWith<$Res> {
  factory _$$UserHasPasswordImplCopyWith(_$UserHasPasswordImpl value,
          $Res Function(_$UserHasPasswordImpl) then) =
      __$$UserHasPasswordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String username, bool hasPassword, String profilePictureUrl});
}

/// @nodoc
class __$$UserHasPasswordImplCopyWithImpl<$Res>
    extends _$UserHasPasswordCopyWithImpl<$Res, _$UserHasPasswordImpl>
    implements _$$UserHasPasswordImplCopyWith<$Res> {
  __$$UserHasPasswordImplCopyWithImpl(
      _$UserHasPasswordImpl _value, $Res Function(_$UserHasPasswordImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserHasPassword
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? hasPassword = null,
    Object? profilePictureUrl = null,
  }) {
    return _then(_$UserHasPasswordImpl(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      hasPassword: null == hasPassword
          ? _value.hasPassword
          : hasPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      profilePictureUrl: null == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserHasPasswordImpl implements _UserHasPassword {
  _$UserHasPasswordImpl(
      {required this.username,
      required this.hasPassword,
      this.profilePictureUrl = ''});

  factory _$UserHasPasswordImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserHasPasswordImplFromJson(json);

  @override
  final String username;
  @override
  final bool hasPassword;
  @override
  @JsonKey()
  final String profilePictureUrl;

  @override
  String toString() {
    return 'UserHasPassword(username: $username, hasPassword: $hasPassword, profilePictureUrl: $profilePictureUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserHasPasswordImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.hasPassword, hasPassword) ||
                other.hasPassword == hasPassword) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, username, hasPassword, profilePictureUrl);

  /// Create a copy of UserHasPassword
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserHasPasswordImplCopyWith<_$UserHasPasswordImpl> get copyWith =>
      __$$UserHasPasswordImplCopyWithImpl<_$UserHasPasswordImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserHasPasswordImplToJson(
      this,
    );
  }
}

abstract class _UserHasPassword implements UserHasPassword {
  factory _UserHasPassword(
      {required final String username,
      required final bool hasPassword,
      final String profilePictureUrl}) = _$UserHasPasswordImpl;

  factory _UserHasPassword.fromJson(Map<String, dynamic> json) =
      _$UserHasPasswordImpl.fromJson;

  @override
  String get username;
  @override
  bool get hasPassword;
  @override
  String get profilePictureUrl;

  /// Create a copy of UserHasPassword
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserHasPasswordImplCopyWith<_$UserHasPasswordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserExistCheck _$UserExistCheckFromJson(Map<String, dynamic> json) {
  return _UserExistCheck.fromJson(json);
}

/// @nodoc
mixin _$UserExistCheck {
  bool get isEmailRegistered => throw _privateConstructorUsedError;
  bool get isGlobal => throw _privateConstructorUsedError;
  List<UserHasPassword> get users => throw _privateConstructorUsedError;

  /// Serializes this UserExistCheck to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserExistCheck
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserExistCheckCopyWith<UserExistCheck> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserExistCheckCopyWith<$Res> {
  factory $UserExistCheckCopyWith(
          UserExistCheck value, $Res Function(UserExistCheck) then) =
      _$UserExistCheckCopyWithImpl<$Res, UserExistCheck>;
  @useResult
  $Res call(
      {bool isEmailRegistered, bool isGlobal, List<UserHasPassword> users});
}

/// @nodoc
class _$UserExistCheckCopyWithImpl<$Res, $Val extends UserExistCheck>
    implements $UserExistCheckCopyWith<$Res> {
  _$UserExistCheckCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserExistCheck
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isEmailRegistered = null,
    Object? isGlobal = null,
    Object? users = null,
  }) {
    return _then(_value.copyWith(
      isEmailRegistered: null == isEmailRegistered
          ? _value.isEmailRegistered
          : isEmailRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      isGlobal: null == isGlobal
          ? _value.isGlobal
          : isGlobal // ignore: cast_nullable_to_non_nullable
              as bool,
      users: null == users
          ? _value.users
          : users // ignore: cast_nullable_to_non_nullable
              as List<UserHasPassword>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserExistCheckImplCopyWith<$Res>
    implements $UserExistCheckCopyWith<$Res> {
  factory _$$UserExistCheckImplCopyWith(_$UserExistCheckImpl value,
          $Res Function(_$UserExistCheckImpl) then) =
      __$$UserExistCheckImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isEmailRegistered, bool isGlobal, List<UserHasPassword> users});
}

/// @nodoc
class __$$UserExistCheckImplCopyWithImpl<$Res>
    extends _$UserExistCheckCopyWithImpl<$Res, _$UserExistCheckImpl>
    implements _$$UserExistCheckImplCopyWith<$Res> {
  __$$UserExistCheckImplCopyWithImpl(
      _$UserExistCheckImpl _value, $Res Function(_$UserExistCheckImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserExistCheck
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isEmailRegistered = null,
    Object? isGlobal = null,
    Object? users = null,
  }) {
    return _then(_$UserExistCheckImpl(
      isEmailRegistered: null == isEmailRegistered
          ? _value.isEmailRegistered
          : isEmailRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      isGlobal: null == isGlobal
          ? _value.isGlobal
          : isGlobal // ignore: cast_nullable_to_non_nullable
              as bool,
      users: null == users
          ? _value._users
          : users // ignore: cast_nullable_to_non_nullable
              as List<UserHasPassword>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserExistCheckImpl implements _UserExistCheck {
  _$UserExistCheckImpl(
      {this.isEmailRegistered = false,
      this.isGlobal = false,
      final List<UserHasPassword> users = const []})
      : _users = users;

  factory _$UserExistCheckImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserExistCheckImplFromJson(json);

  @override
  @JsonKey()
  final bool isEmailRegistered;
  @override
  @JsonKey()
  final bool isGlobal;
  final List<UserHasPassword> _users;
  @override
  @JsonKey()
  List<UserHasPassword> get users {
    if (_users is EqualUnmodifiableListView) return _users;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_users);
  }

  @override
  String toString() {
    return 'UserExistCheck(isEmailRegistered: $isEmailRegistered, isGlobal: $isGlobal, users: $users)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserExistCheckImpl &&
            (identical(other.isEmailRegistered, isEmailRegistered) ||
                other.isEmailRegistered == isEmailRegistered) &&
            (identical(other.isGlobal, isGlobal) ||
                other.isGlobal == isGlobal) &&
            const DeepCollectionEquality().equals(other._users, _users));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, isEmailRegistered, isGlobal,
      const DeepCollectionEquality().hash(_users));

  /// Create a copy of UserExistCheck
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserExistCheckImplCopyWith<_$UserExistCheckImpl> get copyWith =>
      __$$UserExistCheckImplCopyWithImpl<_$UserExistCheckImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserExistCheckImplToJson(
      this,
    );
  }
}

abstract class _UserExistCheck implements UserExistCheck {
  factory _UserExistCheck(
      {final bool isEmailRegistered,
      final bool isGlobal,
      final List<UserHasPassword> users}) = _$UserExistCheckImpl;

  factory _UserExistCheck.fromJson(Map<String, dynamic> json) =
      _$UserExistCheckImpl.fromJson;

  @override
  bool get isEmailRegistered;
  @override
  bool get isGlobal;
  @override
  List<UserHasPassword> get users;

  /// Create a copy of UserExistCheck
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserExistCheckImplCopyWith<_$UserExistCheckImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CheckEmailRequest _$CheckEmailRequestFromJson(Map<String, dynamic> json) {
  return _CheckEmailRequest.fromJson(json);
}

/// @nodoc
mixin _$CheckEmailRequest {
  String get email => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;

  /// Serializes this CheckEmailRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CheckEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckEmailRequestCopyWith<CheckEmailRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckEmailRequestCopyWith<$Res> {
  factory $CheckEmailRequestCopyWith(
          CheckEmailRequest value, $Res Function(CheckEmailRequest) then) =
      _$CheckEmailRequestCopyWithImpl<$Res, CheckEmailRequest>;
  @useResult
  $Res call({String email, String countryCode});
}

/// @nodoc
class _$CheckEmailRequestCopyWithImpl<$Res, $Val extends CheckEmailRequest>
    implements $CheckEmailRequestCopyWith<$Res> {
  _$CheckEmailRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
  }) {
    return _then(_value.copyWith(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CheckEmailRequestImplCopyWith<$Res>
    implements $CheckEmailRequestCopyWith<$Res> {
  factory _$$CheckEmailRequestImplCopyWith(_$CheckEmailRequestImpl value,
          $Res Function(_$CheckEmailRequestImpl) then) =
      __$$CheckEmailRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String email, String countryCode});
}

/// @nodoc
class __$$CheckEmailRequestImplCopyWithImpl<$Res>
    extends _$CheckEmailRequestCopyWithImpl<$Res, _$CheckEmailRequestImpl>
    implements _$$CheckEmailRequestImplCopyWith<$Res> {
  __$$CheckEmailRequestImplCopyWithImpl(_$CheckEmailRequestImpl _value,
      $Res Function(_$CheckEmailRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
  }) {
    return _then(_$CheckEmailRequestImpl(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckEmailRequestImpl implements _CheckEmailRequest {
  _$CheckEmailRequestImpl({required this.email, required this.countryCode});

  factory _$CheckEmailRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckEmailRequestImplFromJson(json);

  @override
  final String email;
  @override
  final String countryCode;

  @override
  String toString() {
    return 'CheckEmailRequest(email: $email, countryCode: $countryCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckEmailRequestImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, email, countryCode);

  /// Create a copy of CheckEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckEmailRequestImplCopyWith<_$CheckEmailRequestImpl> get copyWith =>
      __$$CheckEmailRequestImplCopyWithImpl<_$CheckEmailRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckEmailRequestImplToJson(
      this,
    );
  }
}

abstract class _CheckEmailRequest implements CheckEmailRequest {
  factory _CheckEmailRequest(
      {required final String email,
      required final String countryCode}) = _$CheckEmailRequestImpl;

  factory _CheckEmailRequest.fromJson(Map<String, dynamic> json) =
      _$CheckEmailRequestImpl.fromJson;

  @override
  String get email;
  @override
  String get countryCode;

  /// Create a copy of CheckEmailRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckEmailRequestImplCopyWith<_$CheckEmailRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
