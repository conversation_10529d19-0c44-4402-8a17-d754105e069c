import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_cubit.dart';
import 'package:koc_app/src/modules/authentication/cubit/authentication_state.dart';
import 'package:koc_app/src/modules/authentication/presentation/page/base_sign_in_page.dart';
import 'package:koc_app/src/modules/shared/model/otp_endpoint.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/validator/validators.dart';
import 'package:koc_app/src/shared/widgets/common_elevated_button.dart';

class SignInByPasswordPage extends StatefulWidget {
  const SignInByPasswordPage({super.key});

  @override
  State<SignInByPasswordPage> createState() => _SignInByPasswordPageState();
}

class _SignInByPasswordPageState extends BaseSignInPageState<SignInByPasswordPage> {
  late final SiteCubit siteCubit = Modular.get<SiteCubit>();
  final TextEditingController _passwordController = TextEditingController();

  @override
  Widget buildFooter() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<AuthenticationCubit, AuthenticationState>(builder: (context, state) {
            return TextField(
              controller: _passwordController,
              style: Theme.of(context).textTheme.bodySmall,
              obscureText: !state.obscurePassword,
              onChanged: (value) {
                cubit.setPasswordValid(Validators.isValidPassword(value));
              },
              decoration: InputDecoration(
                  labelText: 'Password',
                  suffixIcon: IconButton(
                    icon: Icon(
                      state.obscurePassword ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      cubit.toggleObscurePassword();
                    },
                  )),
            );
          }),
          const Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () {
                  Modular.to.pushNamed('/forgot-password',
                      arguments: ['/verify-identity/${cubit.state.email}', OtpEndpoint.resetPassword]);
                },
                child: Text(
                  'Forgot password?',
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(color: const Color(0xFFEF6507)),
                ),
              ),
              BlocBuilder<AuthenticationCubit, AuthenticationState>(builder: (context, state) {
                return CommonElevatedButton('Log in', () async {
                  commonCubit.showButtonLoading();
                  final result = await cubit.verifyEmailPassword(state.email, _passwordController.text);
                  if (result) {
                    if (cubit.state.authTokenInfo?.token.isNotEmpty ?? false) {
                      siteCubit.setSite(await cubit.commonCubit.sharedPreferencesService.getSites(),
                          await cubit.commonCubit.sharedPreferencesService.getCurrentSiteId() ?? 0);
                      Modular.to.navigate("/navigation");
                    }
                  } else if (mounted) {
                    context.showSnackBar(cubit.state.errorMessage);
                  }
                  commonCubit.hideButtonLoading();
                });
              }),
            ],
          ),
        ],
      ),
    );
  }
}
