import 'package:flutter/material.dart';
import 'package:koc_app/src/modules/authentication/presentation/widget/text_link.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';

class TermsAndPrivacyWidget extends StatelessWidget {
  final TextStyle? textStyle;

  final String termsUrl;

  final String privacyUrl;

  const TermsAndPrivacyWidget({
    super.key,
    this.textStyle,
    this.termsUrl = 'https://accesstrade.global/terms-of-service',
    this.privacyUrl = 'https://accesstrade.global/privacy-policy',
  });

  @override
  Widget build(BuildContext context) {
    final style = textStyle ?? context.textLabelMedium(color: ColorConstants.textColor);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'By signing up, you agree to',
          style: style,
        ),
        Row(
          children: [
            Text(
              'ACCESSTRADE\'s ',
              style: style,
            ),
            TextLink(
              text: 'Terms',
              url: termsUrl,
              textStyle: style,
            ),
            Text(
              ' and ',
              style: style,
            ),
            TextLink(
              text: 'Privacy Policy',
              url: privacyUrl,
              textStyle: style,
            ),
          ],
        ),
      ],
    );
  }
}
