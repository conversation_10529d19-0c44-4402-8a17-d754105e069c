import 'package:freezed_annotation/freezed_annotation.dart';

part 'survey_tab_state.freezed.dart';
part 'survey_tab_state.g.dart';

@freezed
class SurveyTabState with _$SurveyTabState {
  const factory SurveyTabState({
    @Default(0) int currentTab,
    @Default('') String socialMediaName,
    @Default('') String socialMediaUrl,
    @Default('') String websiteName,
    @Default('') String websiteUrl,
  }) = _SurveyTabState;

  factory SurveyTabState.fromJson(Map<String, Object?> json) =>
      _$SurveyTabStateFromJson(json);
}
