// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'survey_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AddPartnerRequest _$AddPartnerRequestFromJson(Map<String, dynamic> json) {
  return _AddPartnerRequest.fromJson(json);
}

/// @nodoc
mixin _$AddPartnerRequest {
  String get email => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get siteName => throw _privateConstructorUsedError;
  String get siteUrl => throw _privateConstructorUsedError;
  String get socialMediaType => throw _privateConstructorUsedError;
  String? get socialMediaFollower => throw _privateConstructorUsedError;

  /// Serializes this AddPartnerRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AddPartnerRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddPartnerRequestCopyWith<AddPartnerRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddPartnerRequestCopyWith<$Res> {
  factory $AddPartnerRequestCopyWith(
          AddPartnerRequest value, $Res Function(AddPartnerRequest) then) =
      _$AddPartnerRequestCopyWithImpl<$Res, AddPartnerRequest>;
  @useResult
  $Res call(
      {String email,
      String countryCode,
      String firstName,
      String lastName,
      String siteName,
      String siteUrl,
      String socialMediaType,
      String? socialMediaFollower});
}

/// @nodoc
class _$AddPartnerRequestCopyWithImpl<$Res, $Val extends AddPartnerRequest>
    implements $AddPartnerRequestCopyWith<$Res> {
  _$AddPartnerRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddPartnerRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? siteName = null,
    Object? siteUrl = null,
    Object? socialMediaType = null,
    Object? socialMediaFollower = freezed,
  }) {
    return _then(_value.copyWith(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      siteName: null == siteName
          ? _value.siteName
          : siteName // ignore: cast_nullable_to_non_nullable
              as String,
      siteUrl: null == siteUrl
          ? _value.siteUrl
          : siteUrl // ignore: cast_nullable_to_non_nullable
              as String,
      socialMediaType: null == socialMediaType
          ? _value.socialMediaType
          : socialMediaType // ignore: cast_nullable_to_non_nullable
              as String,
      socialMediaFollower: freezed == socialMediaFollower
          ? _value.socialMediaFollower
          : socialMediaFollower // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddPartnerRequestImplCopyWith<$Res>
    implements $AddPartnerRequestCopyWith<$Res> {
  factory _$$AddPartnerRequestImplCopyWith(_$AddPartnerRequestImpl value,
          $Res Function(_$AddPartnerRequestImpl) then) =
      __$$AddPartnerRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String email,
      String countryCode,
      String firstName,
      String lastName,
      String siteName,
      String siteUrl,
      String socialMediaType,
      String? socialMediaFollower});
}

/// @nodoc
class __$$AddPartnerRequestImplCopyWithImpl<$Res>
    extends _$AddPartnerRequestCopyWithImpl<$Res, _$AddPartnerRequestImpl>
    implements _$$AddPartnerRequestImplCopyWith<$Res> {
  __$$AddPartnerRequestImplCopyWithImpl(_$AddPartnerRequestImpl _value,
      $Res Function(_$AddPartnerRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddPartnerRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? countryCode = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? siteName = null,
    Object? siteUrl = null,
    Object? socialMediaType = null,
    Object? socialMediaFollower = freezed,
  }) {
    return _then(_$AddPartnerRequestImpl(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      siteName: null == siteName
          ? _value.siteName
          : siteName // ignore: cast_nullable_to_non_nullable
              as String,
      siteUrl: null == siteUrl
          ? _value.siteUrl
          : siteUrl // ignore: cast_nullable_to_non_nullable
              as String,
      socialMediaType: null == socialMediaType
          ? _value.socialMediaType
          : socialMediaType // ignore: cast_nullable_to_non_nullable
              as String,
      socialMediaFollower: freezed == socialMediaFollower
          ? _value.socialMediaFollower
          : socialMediaFollower // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddPartnerRequestImpl implements _AddPartnerRequest {
  const _$AddPartnerRequestImpl(
      {required this.email,
      required this.countryCode,
      required this.firstName,
      required this.lastName,
      required this.siteName,
      required this.siteUrl,
      required this.socialMediaType,
      this.socialMediaFollower});

  factory _$AddPartnerRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$AddPartnerRequestImplFromJson(json);

  @override
  final String email;
  @override
  final String countryCode;
  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String siteName;
  @override
  final String siteUrl;
  @override
  final String socialMediaType;
  @override
  final String? socialMediaFollower;

  @override
  String toString() {
    return 'AddPartnerRequest(email: $email, countryCode: $countryCode, firstName: $firstName, lastName: $lastName, siteName: $siteName, siteUrl: $siteUrl, socialMediaType: $socialMediaType, socialMediaFollower: $socialMediaFollower)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddPartnerRequestImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.siteName, siteName) ||
                other.siteName == siteName) &&
            (identical(other.siteUrl, siteUrl) || other.siteUrl == siteUrl) &&
            (identical(other.socialMediaType, socialMediaType) ||
                other.socialMediaType == socialMediaType) &&
            (identical(other.socialMediaFollower, socialMediaFollower) ||
                other.socialMediaFollower == socialMediaFollower));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, email, countryCode, firstName,
      lastName, siteName, siteUrl, socialMediaType, socialMediaFollower);

  /// Create a copy of AddPartnerRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddPartnerRequestImplCopyWith<_$AddPartnerRequestImpl> get copyWith =>
      __$$AddPartnerRequestImplCopyWithImpl<_$AddPartnerRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddPartnerRequestImplToJson(
      this,
    );
  }
}

abstract class _AddPartnerRequest implements AddPartnerRequest {
  const factory _AddPartnerRequest(
      {required final String email,
      required final String countryCode,
      required final String firstName,
      required final String lastName,
      required final String siteName,
      required final String siteUrl,
      required final String socialMediaType,
      final String? socialMediaFollower}) = _$AddPartnerRequestImpl;

  factory _AddPartnerRequest.fromJson(Map<String, dynamic> json) =
      _$AddPartnerRequestImpl.fromJson;

  @override
  String get email;
  @override
  String get countryCode;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get siteName;
  @override
  String get siteUrl;
  @override
  String get socialMediaType;
  @override
  String? get socialMediaFollower;

  /// Create a copy of AddPartnerRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddPartnerRequestImplCopyWith<_$AddPartnerRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) {
  return _UserInfo.fromJson(json);
}

/// @nodoc
mixin _$UserInfo {
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;

  /// Serializes this UserInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserInfoCopyWith<UserInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserInfoCopyWith<$Res> {
  factory $UserInfoCopyWith(UserInfo value, $Res Function(UserInfo) then) =
      _$UserInfoCopyWithImpl<$Res, UserInfo>;
  @useResult
  $Res call({String firstName, String lastName});
}

/// @nodoc
class _$UserInfoCopyWithImpl<$Res, $Val extends UserInfo>
    implements $UserInfoCopyWith<$Res> {
  _$UserInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
  }) {
    return _then(_value.copyWith(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserInfoImplCopyWith<$Res>
    implements $UserInfoCopyWith<$Res> {
  factory _$$UserInfoImplCopyWith(
          _$UserInfoImpl value, $Res Function(_$UserInfoImpl) then) =
      __$$UserInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String firstName, String lastName});
}

/// @nodoc
class __$$UserInfoImplCopyWithImpl<$Res>
    extends _$UserInfoCopyWithImpl<$Res, _$UserInfoImpl>
    implements _$$UserInfoImplCopyWith<$Res> {
  __$$UserInfoImplCopyWithImpl(
      _$UserInfoImpl _value, $Res Function(_$UserInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
  }) {
    return _then(_$UserInfoImpl(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserInfoImpl implements _UserInfo {
  const _$UserInfoImpl({this.firstName = '', this.lastName = ''});

  factory _$UserInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserInfoImplFromJson(json);

  @override
  @JsonKey()
  final String firstName;
  @override
  @JsonKey()
  final String lastName;

  @override
  String toString() {
    return 'UserInfo(firstName: $firstName, lastName: $lastName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserInfoImpl &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, firstName, lastName);

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserInfoImplCopyWith<_$UserInfoImpl> get copyWith =>
      __$$UserInfoImplCopyWithImpl<_$UserInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserInfoImplToJson(
      this,
    );
  }
}

abstract class _UserInfo implements UserInfo {
  const factory _UserInfo({final String firstName, final String lastName}) =
      _$UserInfoImpl;

  factory _UserInfo.fromJson(Map<String, dynamic> json) =
      _$UserInfoImpl.fromJson;

  @override
  String get firstName;
  @override
  String get lastName;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserInfoImplCopyWith<_$UserInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SocialInfo _$SocialInfoFromJson(Map<String, dynamic> json) {
  return _SocialInfo.fromJson(json);
}

/// @nodoc
mixin _$SocialInfo {
  int? get id => throw _privateConstructorUsedError;
  SocialType get socialMediaType => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  FollowerNumber get totalFollowerLevel => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Serializes this SocialInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SocialInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SocialInfoCopyWith<SocialInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SocialInfoCopyWith<$Res> {
  factory $SocialInfoCopyWith(
          SocialInfo value, $Res Function(SocialInfo) then) =
      _$SocialInfoCopyWithImpl<$Res, SocialInfo>;
  @useResult
  $Res call(
      {int? id,
      SocialType socialMediaType,
      String url,
      String name,
      FollowerNumber totalFollowerLevel,
      String? errorMessage});
}

/// @nodoc
class _$SocialInfoCopyWithImpl<$Res, $Val extends SocialInfo>
    implements $SocialInfoCopyWith<$Res> {
  _$SocialInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SocialInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? socialMediaType = null,
    Object? url = null,
    Object? name = null,
    Object? totalFollowerLevel = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      socialMediaType: null == socialMediaType
          ? _value.socialMediaType
          : socialMediaType // ignore: cast_nullable_to_non_nullable
              as SocialType,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      totalFollowerLevel: null == totalFollowerLevel
          ? _value.totalFollowerLevel
          : totalFollowerLevel // ignore: cast_nullable_to_non_nullable
              as FollowerNumber,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SocialInfoImplCopyWith<$Res>
    implements $SocialInfoCopyWith<$Res> {
  factory _$$SocialInfoImplCopyWith(
          _$SocialInfoImpl value, $Res Function(_$SocialInfoImpl) then) =
      __$$SocialInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      SocialType socialMediaType,
      String url,
      String name,
      FollowerNumber totalFollowerLevel,
      String? errorMessage});
}

/// @nodoc
class __$$SocialInfoImplCopyWithImpl<$Res>
    extends _$SocialInfoCopyWithImpl<$Res, _$SocialInfoImpl>
    implements _$$SocialInfoImplCopyWith<$Res> {
  __$$SocialInfoImplCopyWithImpl(
      _$SocialInfoImpl _value, $Res Function(_$SocialInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of SocialInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? socialMediaType = null,
    Object? url = null,
    Object? name = null,
    Object? totalFollowerLevel = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$SocialInfoImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      socialMediaType: null == socialMediaType
          ? _value.socialMediaType
          : socialMediaType // ignore: cast_nullable_to_non_nullable
              as SocialType,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      totalFollowerLevel: null == totalFollowerLevel
          ? _value.totalFollowerLevel
          : totalFollowerLevel // ignore: cast_nullable_to_non_nullable
              as FollowerNumber,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SocialInfoImpl implements _SocialInfo {
  const _$SocialInfoImpl(
      {this.id,
      this.socialMediaType = SocialType.OTHER,
      this.url = '',
      this.name = '',
      this.totalFollowerLevel = FollowerNumber.EMPTY,
      this.errorMessage});

  factory _$SocialInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$SocialInfoImplFromJson(json);

  @override
  final int? id;
  @override
  @JsonKey()
  final SocialType socialMediaType;
  @override
  @JsonKey()
  final String url;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final FollowerNumber totalFollowerLevel;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'SocialInfo(id: $id, socialMediaType: $socialMediaType, url: $url, name: $name, totalFollowerLevel: $totalFollowerLevel, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SocialInfoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.socialMediaType, socialMediaType) ||
                other.socialMediaType == socialMediaType) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.totalFollowerLevel, totalFollowerLevel) ||
                other.totalFollowerLevel == totalFollowerLevel) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, socialMediaType, url, name,
      totalFollowerLevel, errorMessage);

  /// Create a copy of SocialInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SocialInfoImplCopyWith<_$SocialInfoImpl> get copyWith =>
      __$$SocialInfoImplCopyWithImpl<_$SocialInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SocialInfoImplToJson(
      this,
    );
  }
}

abstract class _SocialInfo implements SocialInfo {
  const factory _SocialInfo(
      {final int? id,
      final SocialType socialMediaType,
      final String url,
      final String name,
      final FollowerNumber totalFollowerLevel,
      final String? errorMessage}) = _$SocialInfoImpl;

  factory _SocialInfo.fromJson(Map<String, dynamic> json) =
      _$SocialInfoImpl.fromJson;

  @override
  int? get id;
  @override
  SocialType get socialMediaType;
  @override
  String get url;
  @override
  String get name;
  @override
  FollowerNumber get totalFollowerLevel;
  @override
  String? get errorMessage;

  /// Create a copy of SocialInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SocialInfoImplCopyWith<_$SocialInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PassionateInfo _$PassionateInfoFromJson(Map<String, dynamic> json) {
  return _PassionateInfo.fromJson(json);
}

/// @nodoc
mixin _$PassionateInfo {
  List<PassionateItem> get selectedPassionateItems =>
      throw _privateConstructorUsedError;

  /// Serializes this PassionateInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PassionateInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PassionateInfoCopyWith<PassionateInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PassionateInfoCopyWith<$Res> {
  factory $PassionateInfoCopyWith(
          PassionateInfo value, $Res Function(PassionateInfo) then) =
      _$PassionateInfoCopyWithImpl<$Res, PassionateInfo>;
  @useResult
  $Res call({List<PassionateItem> selectedPassionateItems});
}

/// @nodoc
class _$PassionateInfoCopyWithImpl<$Res, $Val extends PassionateInfo>
    implements $PassionateInfoCopyWith<$Res> {
  _$PassionateInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PassionateInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedPassionateItems = null,
  }) {
    return _then(_value.copyWith(
      selectedPassionateItems: null == selectedPassionateItems
          ? _value.selectedPassionateItems
          : selectedPassionateItems // ignore: cast_nullable_to_non_nullable
              as List<PassionateItem>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PassionateInfoImplCopyWith<$Res>
    implements $PassionateInfoCopyWith<$Res> {
  factory _$$PassionateInfoImplCopyWith(_$PassionateInfoImpl value,
          $Res Function(_$PassionateInfoImpl) then) =
      __$$PassionateInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PassionateItem> selectedPassionateItems});
}

/// @nodoc
class __$$PassionateInfoImplCopyWithImpl<$Res>
    extends _$PassionateInfoCopyWithImpl<$Res, _$PassionateInfoImpl>
    implements _$$PassionateInfoImplCopyWith<$Res> {
  __$$PassionateInfoImplCopyWithImpl(
      _$PassionateInfoImpl _value, $Res Function(_$PassionateInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of PassionateInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedPassionateItems = null,
  }) {
    return _then(_$PassionateInfoImpl(
      selectedPassionateItems: null == selectedPassionateItems
          ? _value._selectedPassionateItems
          : selectedPassionateItems // ignore: cast_nullable_to_non_nullable
              as List<PassionateItem>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PassionateInfoImpl implements _PassionateInfo {
  const _$PassionateInfoImpl(
      {final List<PassionateItem> selectedPassionateItems =
          const <PassionateItem>[]})
      : _selectedPassionateItems = selectedPassionateItems;

  factory _$PassionateInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$PassionateInfoImplFromJson(json);

  final List<PassionateItem> _selectedPassionateItems;
  @override
  @JsonKey()
  List<PassionateItem> get selectedPassionateItems {
    if (_selectedPassionateItems is EqualUnmodifiableListView)
      return _selectedPassionateItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedPassionateItems);
  }

  @override
  String toString() {
    return 'PassionateInfo(selectedPassionateItems: $selectedPassionateItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PassionateInfoImpl &&
            const DeepCollectionEquality().equals(
                other._selectedPassionateItems, _selectedPassionateItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_selectedPassionateItems));

  /// Create a copy of PassionateInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PassionateInfoImplCopyWith<_$PassionateInfoImpl> get copyWith =>
      __$$PassionateInfoImplCopyWithImpl<_$PassionateInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PassionateInfoImplToJson(
      this,
    );
  }
}

abstract class _PassionateInfo implements PassionateInfo {
  const factory _PassionateInfo(
          {final List<PassionateItem> selectedPassionateItems}) =
      _$PassionateInfoImpl;

  factory _PassionateInfo.fromJson(Map<String, dynamic> json) =
      _$PassionateInfoImpl.fromJson;

  @override
  List<PassionateItem> get selectedPassionateItems;

  /// Create a copy of PassionateInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PassionateInfoImplCopyWith<_$PassionateInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
