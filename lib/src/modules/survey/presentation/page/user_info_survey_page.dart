import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_cubit.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_state.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';
import 'package:koc_app/src/shared/widgets/common_elevated_button.dart';

class UserInfoSurveyPage extends StatefulWidget {
  const UserInfoSurveyPage({super.key});

  @override
  State<UserInfoSurveyPage> createState() => _UserInfoSurveyPageState();
}

class _UserInfoSurveyPageState extends BasePageState<UserInfoSurveyPage, SurveyCubit> {
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final SharedPreferencesService _sharedPreferencesService = Modular.get<SharedPreferencesService>();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    final firstName = await _sharedPreferencesService.getStringFromInstance('firstName');
    final lastName = await _sharedPreferencesService.getStringFromInstance('lastName');

    if (firstName != null && firstName.isNotEmpty) {
      _firstNameController.text = firstName;
      cubit.saveUserInfo(cubit.state.userInfo.copyWith(firstName: firstName));

      if (lastName != null && lastName.isNotEmpty) {
        _lastNameController.text = lastName;
        cubit.saveUserInfo(cubit.state.userInfo.copyWith(lastName: lastName));
      }
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: Padding(
          padding: EdgeInsets.only(left: 16.r),
          child: buildLogo(),
        ),
      ),
      resizeToAvoidBottomInset: false,
      body: _buildBody(context),
      bottomSheet: _buildButton(),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          _buildProgressBar(),
          SizedBox(height: 16.r),
          _buildHeader(),
          SizedBox(height: 32.r),
          _buildNameSection(),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    return LinearProgressIndicator(
      value: 0.33,
      backgroundColor: const Color(0xFFFFB522).withValues(alpha: 0.3),
      color: const Color(0xFFFFB522),
    );
  }

  Widget _buildButton() {
    return BlocBuilder<SurveyCubit, SurveyState>(
        bloc: cubit,
        builder: (context, state) {
          UserInfo userInfo = state.userInfo;
          return Container(
            color: Colors.white,
            height: 68.r,
            alignment: Alignment.centerRight,
            padding: EdgeInsets.all(16.r),
            child: CommonElevatedButton(
              'Next',
              userInfo.firstName.isNotEmpty ? () => Modular.to.pushNamed("/survey/social") : null,
            ),
          );
        });
  }

  Widget _buildNameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Could you tell us your name?',
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(
          height: 16.r,
        ),
        _buildNameField(_firstNameController, 'First name', (value) {
          cubit.saveUserInfo(cubit.state.userInfo.copyWith(firstName: value));
        }),
        SizedBox(height: 16.r),
        _buildNameField(_lastNameController, 'Last name (Optional)', (value) {
          cubit.saveUserInfo(cubit.state.userInfo.copyWith(lastName: value));
        })
      ],
    );
  }

  Widget _buildNameField(TextEditingController controller, String labelText, Function(String) onChanged) {
    return SizedBox(
      height: 40.r,
      child: TextField(
        controller: controller,
        onChanged: onChanged,
        style: Theme.of(context).textTheme.bodySmall,
        decoration: InputDecoration(
          labelText: labelText,
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'Let\'s customize your account. Tell us about yourself and your company so we can customize your account just for you.',
          style: Theme.of(context).textTheme.bodySmall,
        )
      ],
    );
  }
}
