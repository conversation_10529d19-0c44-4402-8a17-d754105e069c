// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_transaction_report_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CampaignTransactionReportData _$CampaignTransactionReportDataFromJson(
    Map<String, dynamic> json) {
  return _CampaignTransactionReportData.fromJson(json);
}

/// @nodoc
mixin _$CampaignTransactionReportData {
  int get quantity => throw _privateConstructorUsedError;
  double get unitPrice => throw _privateConstructorUsedError;
  double get reward => throw _privateConstructorUsedError;

  /// Serializes this CampaignTransactionReportData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CampaignTransactionReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CampaignTransactionReportDataCopyWith<CampaignTransactionReportData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CampaignTransactionReportDataCopyWith<$Res> {
  factory $CampaignTransactionReportDataCopyWith(
          CampaignTransactionReportData value,
          $Res Function(CampaignTransactionReportData) then) =
      _$CampaignTransactionReportDataCopyWithImpl<$Res,
          CampaignTransactionReportData>;
  @useResult
  $Res call({int quantity, double unitPrice, double reward});
}

/// @nodoc
class _$CampaignTransactionReportDataCopyWithImpl<$Res,
        $Val extends CampaignTransactionReportData>
    implements $CampaignTransactionReportDataCopyWith<$Res> {
  _$CampaignTransactionReportDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CampaignTransactionReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quantity = null,
    Object? unitPrice = null,
    Object? reward = null,
  }) {
    return _then(_value.copyWith(
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      unitPrice: null == unitPrice
          ? _value.unitPrice
          : unitPrice // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CampaignTransactionReportDataImplCopyWith<$Res>
    implements $CampaignTransactionReportDataCopyWith<$Res> {
  factory _$$CampaignTransactionReportDataImplCopyWith(
          _$CampaignTransactionReportDataImpl value,
          $Res Function(_$CampaignTransactionReportDataImpl) then) =
      __$$CampaignTransactionReportDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int quantity, double unitPrice, double reward});
}

/// @nodoc
class __$$CampaignTransactionReportDataImplCopyWithImpl<$Res>
    extends _$CampaignTransactionReportDataCopyWithImpl<$Res,
        _$CampaignTransactionReportDataImpl>
    implements _$$CampaignTransactionReportDataImplCopyWith<$Res> {
  __$$CampaignTransactionReportDataImplCopyWithImpl(
      _$CampaignTransactionReportDataImpl _value,
      $Res Function(_$CampaignTransactionReportDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of CampaignTransactionReportData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quantity = null,
    Object? unitPrice = null,
    Object? reward = null,
  }) {
    return _then(_$CampaignTransactionReportDataImpl(
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      unitPrice: null == unitPrice
          ? _value.unitPrice
          : unitPrice // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CampaignTransactionReportDataImpl
    implements _CampaignTransactionReportData {
  _$CampaignTransactionReportDataImpl(
      {this.quantity = 0, this.unitPrice = 0, this.reward = 0});

  factory _$CampaignTransactionReportDataImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CampaignTransactionReportDataImplFromJson(json);

  @override
  @JsonKey()
  final int quantity;
  @override
  @JsonKey()
  final double unitPrice;
  @override
  @JsonKey()
  final double reward;

  @override
  String toString() {
    return 'CampaignTransactionReportData(quantity: $quantity, unitPrice: $unitPrice, reward: $reward)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CampaignTransactionReportDataImpl &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.unitPrice, unitPrice) ||
                other.unitPrice == unitPrice) &&
            (identical(other.reward, reward) || other.reward == reward));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, quantity, unitPrice, reward);

  /// Create a copy of CampaignTransactionReportData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CampaignTransactionReportDataImplCopyWith<
          _$CampaignTransactionReportDataImpl>
      get copyWith => __$$CampaignTransactionReportDataImplCopyWithImpl<
          _$CampaignTransactionReportDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CampaignTransactionReportDataImplToJson(
      this,
    );
  }
}

abstract class _CampaignTransactionReportData
    implements CampaignTransactionReportData {
  factory _CampaignTransactionReportData(
      {final int quantity,
      final double unitPrice,
      final double reward}) = _$CampaignTransactionReportDataImpl;

  factory _CampaignTransactionReportData.fromJson(Map<String, dynamic> json) =
      _$CampaignTransactionReportDataImpl.fromJson;

  @override
  int get quantity;
  @override
  double get unitPrice;
  @override
  double get reward;

  /// Create a copy of CampaignTransactionReportData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CampaignTransactionReportDataImplCopyWith<
          _$CampaignTransactionReportDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
