import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/performance/performance_daily_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/performance/performance_daily_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/performance_daily_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class PerformanceDailyReportPage extends StatefulWidget {
  final String month;
  const PerformanceDailyReportPage(this.month, {super.key});

  @override
  State<PerformanceDailyReportPage> createState() => _PerformanceDailyReportPageState();
}

class _PerformanceDailyReportPageState extends BasePageState<PerformanceDailyReportPage, PerformanceDailyReportCubit>
    with ReportMixin, CommonMixin {
  late FilterCubit reportFilterCubit = Modular.get<FilterCubit>();

  @override
  void initState() {
    initData();
    super.initState();
  }

  Future<void> initData() async {
    cubit.showLoading();
    FilterState reportFilterState = reportFilterCubit.state;
    DateTime month = widget.month.toDateTime(yearMonthFormat);
    await cubit.findConversions(month, DateTime(month.year, month.month + 1, 0), reportFilterState.selectedDateType,
        reportFilterState.selectedStatus, reportFilterState.selectedCampaign, reportFilterState.selectedSite);
    cubit.hideLoading();
  }

  /// Refresh data for pull-to-refresh
  Future<void> _refreshData() async {
    final FilterState reportFilterState = reportFilterCubit.state;
    final DateTime month = widget.month.toDateTime(yearMonthFormat);

    await cubit.pullToRefresh(month, DateTime(month.year, month.month + 1, 0), reportFilterState.selectedDateType,
        reportFilterState.selectedStatus, reportFilterState.selectedCampaign, reportFilterState.selectedSite);
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: Text(widget.month.toDateTime(yearMonthFormat).toMonthAndYear()),
        customAction: BlocBuilder<PerformanceDailyReportCubit, PerformanceDailyReportState>(builder: (_, state) {
          if (state != PerformanceDailyReportState()) {
            return IconButton(
              icon: Icon(
                Icons.file_download_outlined,
                size: 20.r,
              ),
              onPressed: () {
                saveCsvToDownloadFolder(context, convertToCsv(state), _getFileName());
              },
            );
          }
          return const SizedBox.shrink();
        }),
      ),
      body: _buildBody(),
    );
  }

  String _getFileName() {
    return 'PerformanceDailyReport-${widget.month}';
  }

  List<List<dynamic>> convertToCsv(PerformanceDailyReportState state) {
    List<List<dynamic>> result = [
      ['Date', 'Clicks', 'Conversions', 'Reward (${NumberFormat().simpleCurrencySymbol(state.currency)})', 'CVR', 'EPC']
    ];

    for (var report in state.reportData) {
      result.add([
        report.date,
        report.clicks.toCommaSeparated(),
        report.conversions.toCommaSeparated(),
        report.reward.toCommaSeparated(),
        report.conversionRate.toPercentage(),
        report.earningsPerClick.toCommaSeparated(),
      ]);
    }
    return result;
  }

  Widget _buildChartTitle(String title) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: Colors.white,
        border: Border.all(
          width: 1.r,
          color: ColorConstants.borderColor,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: context.textLabelLarge()),
          Icon(
            Icons.arrow_drop_down_outlined,
            size: 12.r,
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<PerformanceDailyReportCubit, PerformanceDailyReportState>(builder: (_, state) {
      if (state != PerformanceDailyReportState()) {
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(16.r),
            child: Column(
              spacing: 16.r,
              children: [
                _buildPerformanceChart(state),
                _buildTable(state),
              ],
            ),
          ),
        ).withPullToRefresh(
          onRefresh: _refreshData,
        );
      }
      return const SizedBox.shrink();
    });
  }

  List<num> _getValues(PerformanceChartTitle title, List<PerformanceDailyReportData> reportData) {
    if (title == PerformanceChartTitle.CLICKS) {
      return reportData.map((e) => e.clicks).toList();
    } else if (title == PerformanceChartTitle.CONVERSIONS) {
      return reportData.map((e) => e.conversions).toList();
    } else if (title == PerformanceChartTitle.EPC) {
      return reportData.map((e) => e.earningsPerClick).toList();
    } else if (title == PerformanceChartTitle.REWARD) {
      return reportData.map((e) => e.reward).toList();
    } else if (title == PerformanceChartTitle.CVR) {
      return reportData.map((e) => e.conversionRate).toList();
    }
    return [];
  }

  Widget _buildPerformanceChart(PerformanceDailyReportState state) {
    final List<String> days =
        state.reportData.map((data) => DateFormat(fullDateFormat).parse(data.date!).toMonthAndDay()).toList();
    final List<num> leftCounts = _getValues(state.left, state.reportData);
    final List<num> rightCounts = _getValues(state.right, state.reportData);
    int maxY = getRoundUpValue(getMaxY(leftCounts, rightCounts));
    return Container(
      padding: EdgeInsets.all(10.r),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Colors.grey[300]!,
          )),
      child: Column(
        spacing: 16.r,
        children: [
          Row(
            spacing: 16.r,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: PopupMenuButton<PerformanceChartTitle>(
                  color: Colors.white,
                  onSelected: (value) {
                    cubit.updateChartTitles(value, state.right);
                  },
                  itemBuilder: (context) {
                    return _getTitles(state.right).map((e) {
                      return PopupMenuItem(
                          value: e,
                          child: Text(
                            e.value,
                            style: context.textLabelLarge(),
                          ));
                    }).toList();
                  },
                  child: _buildChartTitle(state.left.value),
                ),
              ),
              Text(
                'vs',
                style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
              ),
              Expanded(
                child: PopupMenuButton<PerformanceChartTitle>(
                  color: Colors.white,
                  onSelected: (value) {
                    cubit.updateChartTitles(state.left, value);
                  },
                  itemBuilder: (context) {
                    return _getTitles(state.left).map((e) {
                      return PopupMenuItem(
                          value: e,
                          child: Text(
                            e.value,
                            style: context.textLabelLarge(),
                          ));
                    }).toList();
                  },
                  child: _buildChartTitle(state.right.value),
                ),
              )
            ],
          ),
          AspectRatio(
            aspectRatio: 1.5,
            child: LineChart(
              LineChartData(
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 50.r,
                      interval: 1,
                      getTitlesWidget: (value, meta) {
                        int index = value.toInt();
                        if (index == 0 || index == days.length - 1 || index % 2 == 0) {
                          return index >= 0 && index < days.length
                              ? SizedBox(
                                  height: 100.r,
                                  child: Center(
                                    child: Transform.rotate(
                                      angle: -0.8,
                                      child: Text(
                                        days[index],
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelSmall!
                                            .copyWith(fontWeight: FontWeight.w500),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink();
                        } else {
                          return const SizedBox.shrink();
                        }
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 20.r + (6.r * maxY.toString().length),
                      getTitlesWidget: (value, meta) {
                        return Padding(
                          padding: EdgeInsets.only(right: 10.r),
                          child: Text(
                            value.toInt().toString(),
                            style: Theme.of(context).textTheme.labelSmall!.copyWith(fontWeight: FontWeight.w500),
                            textAlign: TextAlign.right,
                          ),
                        );
                      },
                    ),
                  ),
                  rightTitles: const AxisTitles(),
                  topTitles: const AxisTitles(),
                ),
                gridData: const FlGridData(show: true),
                borderData: FlBorderData(show: false),
                minY: 0,
                maxY: maxY.toDouble(),
                lineBarsData: [
                  LineChartBarData(
                    spots: List.generate(leftCounts.length, (index) {
                      return FlSpot(index.toDouble(), leftCounts[index].toDouble());
                    }),
                    isCurved: true,
                    color: Colors.blue[900],
                    dotData: const FlDotData(show: true),
                    belowBarData: BarAreaData(show: false),
                  ),
                  LineChartBarData(
                    spots: List.generate(rightCounts.length, (index) {
                      return FlSpot(index.toDouble(), rightCounts[index].toDouble());
                    }),
                    isCurved: true,
                    color: Colors.red[900],
                    dotData: const FlDotData(show: true),
                    belowBarData: BarAreaData(show: false),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<PerformanceChartTitle> _getTitles(PerformanceChartTitle otherSide) {
    return List.from(PerformanceChartTitle.values)..remove(otherSide);
  }

  Widget _buildTable(PerformanceDailyReportState state) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: const Color(0x1F000000),
            width: 1.r,
          )),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.r),
        child: Row(
          children: [
            DataTable(
              headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
              columns: [
                _buildDataColumn('Date'),
              ],
              rows: state.reportData.map((conversion) {
                return DataRow(cells: [
                  _buildDataCell(DateFormat(fullDateFormat).parse(conversion.date!).toMonthAndDay(),
                      alignment: Alignment.center),
                ]);
              }).toList(),
            ),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
                  columns: [
                    buildDataColumn(context, 'Clicks'),
                    buildDataColumn(context, 'Conversions'),
                    buildDataColumn(context, 'Reward (${NumberFormat().simpleCurrencySymbol(state.currency)})'),
                    buildDataColumn(context, 'CVR'),
                    buildDataColumn(context, 'EPC'),
                  ],
                  rows: state.reportData.map((conversion) {
                    return DataRow(cells: [
                      _buildDataCell(conversion.clicks.toCommaSeparated()),
                      _buildDataCell(conversion.conversions.toCommaSeparated()),
                      _buildDataCell(conversion.reward.toCommaSeparated()),
                      _buildDataCell(conversion.conversionRate.toPercentage()),
                      _buildDataCell(conversion.earningsPerClick.toCommaSeparated()),
                    ]);
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  DataCell _buildDataCell(String text,
      {Color color = const Color(0xFF464646), VoidCallback? onTap, Alignment alignment = Alignment.centerRight}) {
    return DataCell(
      onTap: onTap,
      Align(
        alignment: alignment,
        child: Text(
          text,
          style: Theme.of(context).textTheme.labelLarge!.copyWith(color: color),
        ),
      ),
    );
  }

  DataColumn _buildDataColumn(String text) {
    return DataColumn(
        label: Text(text, style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold)));
  }
}
