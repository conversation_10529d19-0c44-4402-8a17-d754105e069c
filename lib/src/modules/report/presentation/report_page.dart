import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:graphic/graphic.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/report_state.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class ReportPage extends StatefulWidget {
  const ReportPage({super.key});

  @override
  State<ReportPage> createState() => _ReportPageState();
}

class _ReportPageState extends BasePageState<ReportPage, ReportCubit> with ReportMixin, CommonMixin {
  @override
  void initState() {
    initData();
    super.initState();
  }

  void initData() {
    if (cubit.state.isPullToRefresh) {
      cubit.findReportData();
    } else {
      doLoadingAction(() async {
        await cubit.findReportData();
      });
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: buildSiteSelectionTitle(context, 'Report', onTap: initData),
        showNotificationAction: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocBuilder<ReportCubit, ReportState>(
      bloc: cubit,
      builder: (_, state) {
        if (state != ReportState()) {
          return PullToRefreshWrapper(
            onRefresh: () => cubit.pullToRefresh(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  spacing: 16.r,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPerformance(state.currentOneYearClickCount, state.currentOneYearConversionCount),
                    _buildConversions(state),
                    _buildCampaign(state),
                    _buildPayment(state)
                  ],
                ),
              ),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildPayment(ReportState state) {
    return Column(
      spacing: 10.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Payment', () {
          Modular.to.pushNamed('/report/payment');
        }),
        _buildPaymentContent(state),
      ],
    );
  }

  Widget _buildPaymentContent(ReportState state) {
    if (state.paymentSummary == null && state.minimumPaymentDetails == null) {
      return const SizedBox.shrink();
    }
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r), border: Border.all(color: ColorConstants.borderColor, width: 1.r)),
      child: Column(
        children: [
          _buildCardContent(
              Text(
                state.paymentSummary!.lifetimeTotalPaidReward.toPrice(state.currency),
                style: context.textBodySmall(fontWeight: FontWeight.w500),
              ),
              'Your lifetime reward',
              'Total reward paid to you since registration with ACCESSTRADE.'),
          const Divider(
            color: Color(0xFFE7E7E7),
          ),
          _buildCardContent(
              ClipRRect(
                borderRadius: BorderRadius.circular(9999),
                child: Stack(
                  children: [
                    LinearProgressIndicator(
                      color: state.paymentSummary!.availablePayment < state.minimumPaymentDetails!.minimumAmount
                          ? const Color(0xFFFFB522)
                          : const Color(0xFF1AAA55),
                      backgroundColor: Colors.grey[400],
                      value: state.paymentSummary!.availablePayment / state.minimumPaymentDetails!.minimumAmount,
                      minHeight: 28.r,
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      height: 28.r,
                      padding: EdgeInsets.only(left: 12.r),
                      child: Text(
                        state.paymentSummary!.availablePayment.toPrice(state.currency),
                        style: Theme.of(context)
                            .textTheme
                            .labelLarge!
                            .copyWith(fontWeight: FontWeight.w500, color: Colors.white),
                      ),
                    )
                  ],
                ),
              ),
              'Minimum payment amount (${state.minimumPaymentDetails!.minimumAmount.toPrice(state.minimumPaymentDetails!.currency)})',
              'Minimum payment for ${state.country!.name.toTitleCase()} bank account is ${state.country!.localCurrencyCode} ${state.minimumPaymentDetails!.minimumAmount.toCommaSeparated()}, International bank is ${state.country!.internationalCurrencyCode} ${state.country!.internationalMinimumPayment.toCommaSeparated()}'),
        ],
      ),
    );
  }

  Widget _buildCampaign(ReportState state) {
    return Column(
      spacing: 10.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Campaign', () {
          Modular.to.pushNamed('/report/campaign');
        }),
        _buildCampaignChart(state),
      ],
    );
  }

  Widget _buildCampaignChart(ReportState state) {
    if (state.topTenCampaignsClickCount.isEmpty) {
      return const SizedBox.shrink();
    }
    final List<Map<String, dynamic>> data = state.topTenCampaignsClickCount.map((e) {
      return {'campaignName': e.campaignName, 'clickCount': e.clicks};
    }).toList()
      ..sort((a, b) => (a['clickCount'] as num).compareTo(b['clickCount'] as num));
    int maxClicks = state.topTenCampaignsClickCount.isNotEmpty
        ? state.topTenCampaignsClickCount.reduce((a, b) => a.clicks > b.clicks ? a : b).clicks.toInt()
        : 0;
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r),
      ),
      padding: EdgeInsets.symmetric(vertical: 4.r),
      child: Column(
        children: [
          Text('Top 10 Campaigns sorted by Clicks',
              style: Theme.of(context).textTheme.labelMedium!.copyWith(fontWeight: FontWeight.w500)),
          Padding(
            padding: EdgeInsets.only(left: 50.r, right: 12.r),
            child: AspectRatio(
              aspectRatio: 1.5,
              child: Chart(
                data: data,
                variables: {
                  'campaignName': Variable(
                    accessor: (Map map) => map['campaignName'] as String,
                  ),
                  'clickCount': Variable(
                    accessor: (Map map) => map['clickCount'] as num,
                    scale: LinearScale(
                      formatter: (p0) => p0.toInt().toString(),
                      ticks: [0, maxClicks ~/ 2, maxClicks],
                    ),
                  ),
                },
                marks: [
                  IntervalMark(
                    position: Varset('campaignName') * Varset('clickCount'),
                    color: ColorEncode(value: Colors.lightGreen),
                    size: SizeEncode(value: 12.r),
                  ),
                ],
                axes: [
                  AxisGuide(
                    variable: 'campaignName',
                    position: 0,
                    label: LabelStyle(
                        offset: Offset(-4.r, 0),
                        textStyle: Theme.of(context).textTheme.labelSmall,
                        minWidth: 50.r,
                        maxWidth: 70.r,
                        ellipsis: '...',
                        textAlign: TextAlign.right),
                  ),
                  AxisGuide(
                    variable: 'clickCount',
                    position: 0,
                    label: LabelStyle(
                      textStyle: Theme.of(context).textTheme.labelSmall,
                    ),
                  ),
                ],
                coord: RectCoord(
                  transposed: true,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversions(ReportState state) {
    return Column(
      spacing: 10.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Conversion', () {
          Modular.to.pushNamed('/report/conversion');
        }),
        _buildConversion(state),
      ],
    );
  }

  Widget _buildConversion(ReportState state) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r), border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r)),
      child: Column(
        children: [
          _buildCardContent(
              Text(
                state.thisMonthOccurredConversionCount.toCommaSeparated(),
                style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
              ),
              'Conversions occurred this month',
              'Saldo yang akan dibawa ke bulan berikutnya. Apabila tidak ada maka akan bernilai 0'),
          const Divider(
            color: Color(0xFFE7E7E7),
          ),
          _buildCardContent(
              Text(
                state.lastMonthApprovedReward.toPrice(state.currency),
                style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
              ),
              'Reward approved last month',
              'Saldo yang akan dibawa ke bulan berikutnya. Apabila tidak ada maka akan bernilai 0'),
        ],
      ),
    );
  }

  Widget _buildCardContent(Widget data, String description, String dialogContents) {
    return Padding(
      padding: EdgeInsets.all(12.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          data,
          Row(
            spacing: 2.r,
            children: [
              Text(description,
                  style: Theme.of(context)
                      .textTheme
                      .labelLarge!
                      .copyWith(fontWeight: FontWeight.w500, color: const Color(0xFF767676))),
              GestureDetector(
                onTap: () {
                  showDescription(context, description, dialogContents);
                },
                child: Icon(
                  Icons.help_outline,
                  size: 16.r,
                  color: const Color(0xFF767676),
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  Widget _buildPerformance(
      Map<DateTime, int> currentOneYearClickCount, Map<DateTime, int> currentOneYearConversionCount) {
    return Column(
      spacing: 10.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Performance', () {
          Modular.to.pushNamed('/report/performance-monthly');
        }),
        _buildPerformanceChart(currentOneYearClickCount, currentOneYearConversionCount),
      ],
    );
  }

  Widget _buildPerformanceChart(
      Map<DateTime, int> currentOneYearClickCount, Map<DateTime, int> currentOneYearConversionCount) {
    if (currentOneYearClickCount.isEmpty || currentOneYearConversionCount.isEmpty) {
      return const SizedBox.shrink();
    }

    final List<String> days =
        currentOneYearClickCount.entries.map((data) => DateFormat('MMM y').format(data.key)).toList();
    final List<int> clickCount = currentOneYearClickCount.values.toList();
    final List<int> conversionCount = currentOneYearConversionCount.values.toList();
    int maxY = getRoundUpValue(getMaxY(clickCount, conversionCount));
    return AspectRatio(
      aspectRatio: 1.5,
      child: Container(
        padding: EdgeInsets.all(10.r),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: Colors.grey[300]!,
            )),
        child: LineChart(
          LineChartData(
            titlesData: FlTitlesData(
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 50.r,
                  interval: 1,
                  getTitlesWidget: (value, meta) {
                    int index = value.toInt();
                    return index >= 0 && index < days.length
                        ? SizedBox(
                            height: 100.r,
                            child: Center(
                              child: Transform.rotate(
                                angle: -0.8,
                                child: Text(
                                  days[index],
                                  style: Theme.of(context).textTheme.labelSmall!.copyWith(fontWeight: FontWeight.w500),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          )
                        : const SizedBox.shrink();
                  },
                ),
              ),
              leftTitles: AxisTitles(
                axisNameWidget: Text(
                  'Clicks',
                  style: Theme.of(context).textTheme.labelMedium!.copyWith(fontWeight: FontWeight.w500),
                ),
                axisNameSize: 20.r,
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 20.r + (6.r * maxY.toString().length),
                  getTitlesWidget: (value, meta) {
                    return Padding(
                      padding: EdgeInsets.only(right: 10.r),
                      child: Text(
                        value.toInt().toString(),
                        style: Theme.of(context).textTheme.labelSmall!.copyWith(fontWeight: FontWeight.w500),
                        textAlign: TextAlign.right,
                      ),
                    );
                  },
                ),
              ),
              rightTitles: AxisTitles(
                axisNameWidget: Text(
                  'Conversions',
                  style: Theme.of(context).textTheme.labelMedium!.copyWith(fontWeight: FontWeight.w500),
                ),
                axisNameSize: 20.r,
              ),
              topTitles: AxisTitles(
                axisNameWidget: Padding(
                  padding: EdgeInsets.only(bottom: 10.r),
                  child: Text(
                    'Clicks vs Conversions',
                    style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                axisNameSize: 30.r,
              ),
            ),
            gridData: const FlGridData(show: true),
            borderData: FlBorderData(show: false),
            minY: 0,
            maxY: maxY.toDouble(),
            lineBarsData: [
              LineChartBarData(
                spots: List.generate(clickCount.length, (index) {
                  return FlSpot(index.toDouble(), clickCount[index].toDouble());
                }),
                isCurved: true,
                color: Colors.blue[900],
                dotData: const FlDotData(show: true),
                belowBarData: BarAreaData(show: false),
              ),
              LineChartBarData(
                spots: List.generate(conversionCount.length, (index) {
                  return FlSpot(index.toDouble(), conversionCount[index].toDouble());
                }),
                isCurved: true,
                color: Colors.red[900],
                dotData: const FlDotData(show: true),
                belowBarData: BarAreaData(show: false),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
