import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_detail_report_cubit.dart';
import 'package:koc_app/src/modules/report/cubit/campaign/campaign_detail_report_state.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/report/mixin/report_mixin.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/filter_state.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/data/item.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';

class CampaignDetailReportPage extends StatefulWidget {
  final Item campaign;
  const CampaignDetailReportPage(this.campaign, {super.key});

  @override
  State<CampaignDetailReportPage> createState() => _CampaignDetailReportPageState();
}

class _CampaignDetailReportPageState extends BasePageState<CampaignDetailReportPage, CampaignDetailReportCubit>
    with ReportMixin {
  late FilterCubit reportFilterCubit = Modular.get<FilterCubit>();

  @override
  void initState() {
    doLoadingAction(() async {
      FilterState reportFilterState = reportFilterCubit.state;
      await cubit.findConversions(widget.campaign.value, reportFilterState.startDate!, reportFilterState.endDate!,
          reportFilterState.selectedDateType, reportFilterState.selectedSite);
    });
    super.initState();
  }

  /// Refresh data for pull-to-refresh
  Future<void> _refreshData() async {
    final FilterState reportFilterState = reportFilterCubit.state;

    await cubit.pullToRefresh(widget.campaign.value, reportFilterState.startDate!, reportFilterState.endDate!,
        reportFilterState.selectedDateType, reportFilterState.selectedSite);
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: Text(widget.campaign.name),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return PullToRefreshWrapper(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: BlocBuilder<CampaignDetailReportCubit, CampaignDetailReportState>(
              bloc: cubit,
              builder: (_, state) {
                if (state.reportData.isNotEmpty) {
                  return _buildDataTable(state);
                }
                return const SizedBox.shrink();
              }),
        ),
      ),
    );
  }

  Widget _buildDataTable(CampaignDetailReportState state) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: const Color(0x1F000000),
            width: 1.r,
          )),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: Row(
            children: [
              Expanded(
                  child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                    headingRowColor: const WidgetStatePropertyAll(Color(0xFFF2F2F2)),
                    columns: [
                      buildDataColumn(context, 'Transaction ID'),
                      buildDataColumn(context, 'Customer Type'),
                      buildDataColumn(context, 'Click Time'),
                      buildDataColumn(context, 'Conversion Time'),
                      buildDataColumn(context, 'Validated Time'),
                      buildDataColumn(context, 'Reward (${NumberFormat().simpleCurrencySymbol(state.currency)})'),
                      buildDataColumn(context, 'Status'),
                    ],
                    rows: state.reportData.map((data) {
                      return DataRow(cells: [
                        buildDataCell(context, data.verificationId, color: ColorConstants.textButtonColor, onTap: () {
                          Modular.to
                              .pushNamed('/report/campaign/transaction', arguments: [widget.campaign.value, data]);
                        }),
                        buildDataCell(context, data.customerType ?? ''),
                        buildDataCell(context, data.clickTime?.toStandard() ?? ''),
                        buildDataCell(context, data.conversionTime?.toStandard() ?? ''),
                        buildDataCell(context, data.confirmationTime?.toStandard() ?? ''),
                        buildDataCell(context,
                            '${NumberFormat().simpleCurrencySymbol(state.currency)}${data.reward.toCommaSeparated()}'),
                        DataCell(
                          Container(
                            padding: EdgeInsets.all(4.r),
                            decoration:
                                BoxDecoration(borderRadius: BorderRadius.circular(9999), color: _getColor(data.status)),
                            child: Text(
                              data.status.name.toTitleCase(),
                              style: context.textLabelLarge(color: Colors.white),
                            ),
                          ),
                        ),
                      ]);
                    }).toList()),
              ))
            ],
          )),
    );
  }

  Color _getColor(ConversionStatus status) {
    if (status == ConversionStatus.APPROVED) {
      return Colors.green;
    } else if (status == ConversionStatus.REJECTED) {
      return Colors.red;
    }
    return Colors.orange;
  }
}
