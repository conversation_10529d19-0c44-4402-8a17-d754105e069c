import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_process_summary.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/constants/date_time_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/modules/shared/widget/no_data.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/empty_state_widget.dart';

mixin ReportMixin {
  Widget buildAddFilterMessage(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        spacing: 12.r,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          SvgPicture.asset(
            'assets/images/search.svg',
            width: 100.r,
          ),
          Text(
            'Add filters to determine what data is shown in your report.',
            style: Theme.of(context).textTheme.labelLarge!.copyWith(
                  color: const Color(0xFF767676),
                ),
            textAlign: TextAlign.center,
          )
        ],
      ),
    );
  }

  Widget buildNoResultBody(BuildContext context, VoidCallback onTap) {
    return Container(
      padding: EdgeInsets.all(16.r),
      alignment: Alignment.center,
      height: 600.r,
      child: EmptyStateWidget(
        onButtonPressed: onTap,
      ),
    );
  }

  DataCell buildDataCell(BuildContext context, String text,
      {Color color = const Color(0xFF464646), VoidCallback? onTap, Alignment? alignment}) {
    return DataCell(
      onTap: onTap,
      Container(
        alignment: alignment,
        child: Text(
          text,
          style: Theme.of(context).textTheme.labelLarge!.copyWith(color: color),
        ),
      ),
    );
  }

  DataColumn buildDataColumn(BuildContext context, String text) {
    return DataColumn(
        label: Text(text, style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold)));
  }

  Widget buildTotalRewardCard(BuildContext context, String currency, double totalReward) {
    return Container(
        width: double.infinity,
        padding: EdgeInsets.all(8.r),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: ColorConstants.borderColor, width: 1.r)),
        child: Column(
          spacing: 4.r,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${currency.currencySymbol}${totalReward.toCommaSeparated()}',
              style: context.textBodySmall(fontWeight: FontWeight.w500),
            ),
            Text(
              'Affiliate marketing program total reward',
              style: context.textLabelLarge(
                fontWeight: FontWeight.w500,
                color: const Color(0xFF767676),
              ),
            ),
          ],
        ));
  }

  Widget buildCampaignRewardTable(BuildContext context, String currency, List<CampaignReward> campaignRewards) {
    if (campaignRewards.isEmpty) {
      return const NoDataReportWidget();
    }
    return _buildStickyHeaderTable(
      context: context,
      currency: currency,
      campaignRewards: campaignRewards,
    );
  }

  Widget _buildStickyHeaderTable({
    required BuildContext context,
    required String currency,
    required List<CampaignReward> campaignRewards,
  }) {
    final double campaignColumnWidth = 200.r;
    final double monthColumnWidth = 120.r;
    final double rewardColumnWidth = 150.r;

    return _StickyHeaderTableWidget(
      headerBuilder: (scrollController) => Container(
        color: const Color(0xFFF2F2F2),
        child: SingleChildScrollView(
          controller: scrollController,
          scrollDirection: Axis.horizontal,
          physics: const ClampingScrollPhysics(),
          child: Row(
            children: [
              _buildHeaderCell(context, 'Campaign', campaignColumnWidth),
              _buildHeaderCell(context, 'Month', monthColumnWidth),
              _buildHeaderCell(context, 'Reward (${currency.currencySymbol})', rewardColumnWidth,
                  alignment: Alignment.centerRight),
            ],
          ),
        ),
      ),
      bodyBuilder: (scrollController) => SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: SingleChildScrollView(
          controller: scrollController,
          scrollDirection: Axis.horizontal,
          physics: const ClampingScrollPhysics(),
          child: Column(
            children: campaignRewards.map((e) {
              return Container(
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
                  ),
                ),
                child: Row(
                  children: [
                    _buildBodyCell(context, e.campaignName, campaignColumnWidth),
                    _buildBodyCell(
                      context,
                      DateFormat(yearMonthFormat).parse(e.rewardMonth).toMonthAndYear(),
                      monthColumnWidth,
                    ),
                    _buildBodyCell(
                      context,
                      e.reward.toCommaSeparated(),
                      rewardColumnWidth,
                      alignment: Alignment.centerRight,
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text, double width, {Alignment? alignment}) {
    return Container(
      width: width,
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 20.r), // Optimized for touch
      alignment: alignment,
      child: Text(
        text,
        style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildBodyCell(BuildContext context, String text, double width, {Alignment? alignment}) {
    return Container(
      width: width,
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 20.r),
      alignment: alignment,
      child: Text(
        text,
        style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF464646)),
      ),
    );
  }
}

/// A widget that provides synchronized horizontal scrolling between header and body
class _StickyHeaderTableWidget extends StatefulWidget {
  final Widget Function(ScrollController scrollController) headerBuilder;
  final Widget Function(ScrollController scrollController) bodyBuilder;

  const _StickyHeaderTableWidget({
    required this.headerBuilder,
    required this.bodyBuilder,
  });

  @override
  State<_StickyHeaderTableWidget> createState() => _StickyHeaderTableWidgetState();
}

class _StickyHeaderTableWidgetState extends State<_StickyHeaderTableWidget> {
  late ScrollController _headerScrollController;
  late ScrollController _bodyScrollController;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    _headerScrollController = ScrollController();
    _bodyScrollController = ScrollController();

    _headerScrollController.addListener(_onHeaderScroll);
    _bodyScrollController.addListener(_onBodyScroll);
  }

  @override
  void dispose() {
    _headerScrollController.removeListener(_onHeaderScroll);
    _bodyScrollController.removeListener(_onBodyScroll);
    _headerScrollController.dispose();
    _bodyScrollController.dispose();
    super.dispose();
  }

  void _onHeaderScroll() {
    if (_isSyncing) return;
    _syncScrollPosition(_headerScrollController, _bodyScrollController);
  }

  void _onBodyScroll() {
    if (_isSyncing) return;
    _syncScrollPosition(_bodyScrollController, _headerScrollController);
  }

  void _syncScrollPosition(ScrollController source, ScrollController target) {
    if (!source.hasClients || !target.hasClients) return;

    final sourceOffset = source.offset;
    final targetOffset = target.offset;

    if ((sourceOffset - targetOffset).abs() > 0.5) {
      _isSyncing = true;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (target.hasClients) {
          target.jumpTo(sourceOffset);
        }
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          child: widget.headerBuilder(_headerScrollController),
        ),
        Expanded(
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: widget.bodyBuilder(_bodyScrollController),
          ),
        ),
      ],
    );
  }
}
