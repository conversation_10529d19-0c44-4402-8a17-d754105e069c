import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_campaign_search_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_state.dart';
import 'package:koc_app/src/modules/campaign/custom_link/data/model/custom_link.dart';
import 'package:koc_app/src/modules/campaign/custom_link/presentation/custom_link_history_page.dart';
import 'package:koc_app/src/modules/campaign/custom_link/presentation/custom_link_sub_id_page.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/validator/validators.dart';

class CustomLinkGenerationView extends StatefulWidget {
  final int? siteId;
  final int? campaignId;
  final List<String>? customLinkAcceptUrls;
  final VoidCallback? onReload; 

  const CustomLinkGenerationView({super.key, this.customLinkAcceptUrls, this.campaignId, this.siteId, this.onReload});

  @override
  State<CustomLinkGenerationView> createState() => _CustomLinkGenerationViewState();
}

class _CustomLinkGenerationViewState extends State<CustomLinkGenerationView> with CommonMixin {
  bool _isValid = false;
  final TextEditingController _customLinkNameController = TextEditingController();
  final TextEditingController _customLinkUrlController = TextEditingController();
  Timer? _debounce;

  @override
  void dispose() {
    _customLinkNameController.dispose();
    _customLinkUrlController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 12.r,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox.shrink(),
        SizedBox(
          width: double.infinity,
          child: TextField(
            controller: _customLinkNameController,
            style: Theme.of(context).textTheme.labelLarge,
            decoration: InputDecoration(
                labelText: 'Custom name',
                labelStyle: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676))),
            onChanged: (value) {
              setIsValidState();
            },
          ),
        ),
        SizedBox(
          width: double.infinity,
          child: TextField(
            controller: _customLinkUrlController,
            keyboardType: TextInputType.multiline,
            minLines: 3,
            maxLines: 10,
            style: Theme.of(context).textTheme.labelLarge,
            decoration: InputDecoration(
              labelText: 'Custom URL',
              labelStyle: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6.r),
                borderSide: const BorderSide(
                  color: Color(0xFFE7E7E7),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6.r),
                borderSide: BorderSide(
                  color: _hasUrlError() ? Colors.red : Theme.of(context).primaryColor,
                ),
              ),
            ),
            onChanged: (value) {
              if (_debounce?.isActive ?? false) _debounce?.cancel();
              _debounce = Timer(const Duration(milliseconds: 500), () {
                setState(() {});
                setIsValidState();
              });
            },
          ),
        ),
        if (_hasUrlError())
          Padding(
            padding: EdgeInsets.only(top: 4.r, left: 4.r),
            child: Text(
              _getUrlErrorMessage() ?? '',
              style: TextStyle(color: Colors.red, fontSize: 12.r),
            ),
          ),
        if (widget.customLinkAcceptUrls?.isNotEmpty ?? false)
          Padding(
            padding: EdgeInsets.only(top: 8.r),
            child: Text(
              'Accepted URL Format',
              style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676)),
            ),
          ),
        if (widget.customLinkAcceptUrls?.isNotEmpty ?? false)
          Column(
            spacing: 4.r,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 4.r,
                runSpacing: 4.r,
                children: widget.customLinkAcceptUrls
                        ?.map((url) => Container(
                              padding: EdgeInsets.symmetric(vertical: 2.r, horizontal: 4.r),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6.r),
                                border: Border.all(color: const Color(0xFFEFEFEF)),
                              ),
                              child: Text(
                                url,
                                style: Theme.of(context)
                                    .textTheme
                                    .labelMedium!
                                    .copyWith(fontWeight: FontWeight.w500, color: const Color(0xFF767676)),
                              ),
                            ))
                        .toList() ??
                    [],
              ),
            ],
          ),
        Container(
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: ColorConstants.borderColor,
              width: 1.r,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  RichText(
                      text: TextSpan(
                          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
                          children: [
                        const TextSpan(text: 'Sub ID'),
                        TextSpan(
                            text: ' (Optional)',
                            style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676)))
                      ])),
                  IconButton(
                    onPressed: () {
                      CustomLinkCubit customLinkCubit = ReadContext(context).read<CustomLinkCubit>();
                      showModalBottomSheet(
                          useSafeArea: true,
                          isScrollControlled: true,
                          context: context,
                          builder: (BuildContext context) {
                            return BlocProvider.value(
                              value: customLinkCubit,
                              child: CustomLinkSubIdPage(subIds: customLinkCubit.state.subIds),
                            );
                          });
                    },
                    icon: Icon(
                      Icons.arrow_forward,
                      size: 20.r,
                    ),
                  ),
                ],
              ),
              BlocBuilder<CustomLinkCubit, CustomLinkState>(builder: (_, state) {
                List<SubId> subIds = state.subIds;
                if (subIds.isNotEmpty && subIds.any((subId) => subId.name.isNotEmpty || subId.value.isNotEmpty)) {
                  return Column(
                    children: [
                      SizedBox(height: 8.r),
                      Wrap(
                        runSpacing: 4.r,
                        spacing: 4.r,
                        children: subIds
                            .where((subId) => subId.name.isNotEmpty || subId.value.isNotEmpty)
                            .map((subId) => Container(
                                  padding: EdgeInsets.symmetric(vertical: 2.r, horizontal: 4.r),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6.r),
                                      border: Border.all(color: const Color(0xFFEFEFEF))),
                                  child: Text(
                                    '${subId.name}={${subId.value}}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelMedium!
                                        .copyWith(fontWeight: FontWeight.w500, color: const Color(0xFF767676)),
                                  ),
                                ))
                            .toList(),
                      ),
                    ],
                  );
                }
                return const SizedBox.shrink();
              }),
            ],
          ),
        ),
        BlocBuilder<CustomLinkHistoryCubit, CustomLinkHistoryState>(
          builder: (_, state) {
            if (state.totalCount == 0) {
              return const SizedBox.shrink();
            }
            return Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: ColorConstants.borderColor,
                  width: 1.r,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    spacing: 8.r,
                    children: [
                      Text(
                        'History',
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.w500),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 4.r, vertical: 2.r),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12.r),
                          color: const Color(0xFFF2F2F2),
                        ),
                        child: Text(
                          ' ${state.totalCount} ',
                          style: Theme.of(context).textTheme.labelLarge,
                        ),
                      )
                    ],
                  ),
                  IconButton(
                    onPressed: () {
                      _showCustomLinkHistoryModal();
                    },
                    icon: Icon(
                      Icons.arrow_forward,
                      size: 20.r,
                    ),
                  )
                ],
              ),
            );
          },
        ),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isValid
                ? () async {
                    showLoadingDialog(context);
                    CustomLinkCubit customLinkCubit = ReadContext(context).read<CustomLinkCubit>();

                    customLinkCubit.updateState(customLinkCubit.state
                        .copyWith(name: _customLinkNameController.text, shareLink: _customLinkUrlController.text));
                    final result = await customLinkCubit.saveCustomLink(widget.siteId, widget.campaignId!);

                    if (result) {
                      CustomLinkHistoryCubit historyCubit = ReadContext(context).read<CustomLinkHistoryCubit>();
                      await historyCubit.getCustomLinks(widget.campaignId!);
                      historyCubit.setRegisteredNewCustomLink(true);
                      Navigator.of(context).pop();
                      Future.delayed(const Duration(milliseconds: 300), () {
                        _showCustomLinkHistoryModal();
                      });
                    } else {
                      context.showSnackBar('Failed to generate custom link(s). Please try again later.');
                      hideLoadingDialog(context);
                    }
                  }
                : null,
            child: Text(
              'Generate',
              style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  void _showCustomLinkHistoryModal() {
    CustomLinkHistoryCubit historyCubit = ReadContext(context).read<CustomLinkHistoryCubit>();
    showModalBottomSheet(
        useSafeArea: true,
        isScrollControlled: true,
        context: context,
        builder: (BuildContext context) {
          return BlocProvider.value(
            value: historyCubit,
            child: ClipRRect(
                borderRadius: BorderRadius.circular(12.r), child: CustomLinkHistoryPage(campaignId: widget.campaignId)),
          );
        }).then((_) {
      _customLinkNameController.clear();
      _customLinkUrlController.clear();
      setState(() {
        _isValid = false;
      });
      CustomLinkCampaignSearchCubit campaignCubit =
          ReadContext(context).read<CustomLinkCampaignSearchCubit>();
       campaignCubit.clearCampaign();
      CustomLinkCubit customLinkCubit =
          ReadContext(context).read<CustomLinkCubit>();
      customLinkCubit.updateState(customLinkCubit.state.copyWith(
        name: '',
        shareLink: '',
        subIds: [],
      ));
      widget.onReload?.call();
    });
  }

  void setIsValidState() {
    setState(() {
      _isValid = widget.campaignId != null &&
          widget.campaignId! > 0 &&
          _customLinkNameController.text.isNotEmpty &&
          Validators.isValidMultipleCustomLinks(_customLinkUrlController.text, widget.customLinkAcceptUrls ?? []);
    });
  }

  bool _hasUrlError() {
    final text = _customLinkUrlController.text.trim();
    return text.isNotEmpty && !Validators.isValidMultipleCustomLinks(text, widget.customLinkAcceptUrls ?? []);
  }

  String? _getUrlErrorMessage() {
    return Validators.getUrlErrorMessage(_customLinkUrlController.text.trim(), widget.customLinkAcceptUrls ?? []);
  }

  @override
  void didUpdateWidget(CustomLinkGenerationView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.siteId != widget.siteId ||
        oldWidget.campaignId != widget.campaignId) {
      setIsValidState();
    }
  }

}
