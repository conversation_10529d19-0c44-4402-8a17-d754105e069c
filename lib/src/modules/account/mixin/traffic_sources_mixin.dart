import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/account/cubit/traffic_sources_cubit.dart';
import 'package:koc_app/src/modules/account/presentation/page/account_traffic_sources_create_page.dart';
import 'package:koc_app/src/modules/survey/cubit/survey_tab_cubit.dart';

mixin TrafficSourcesMixin {
  void showTrafficSourcesModal(BuildContext context) {
    showModalBottomSheet(
        useSafeArea: true,
        isScrollControlled: true,
        context: context,
        builder: (_) {
          return ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: MultiBlocProvider(
                providers: [
                  BlocProvider.value(
                    value: ReadContext(context).read<TrafficSourcesCubit>(),
                  ),
                  BlocProvider.value(
                    value: ReadContext(context).read<SurveyTabCubit>(),
                  ),
                ],
                child: const AccountTrafficSourcesCreatePage(),
              ));
        });
  }
}
