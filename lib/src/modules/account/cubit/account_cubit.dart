import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_state.dart';
import 'package:koc_app/src/modules/account/data/model/account.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

import '../../../shared/utils/handle_error.dart';
import '../../authentication/data/models/auth_token_info.dart';

class AccountCubit extends BaseCubit<AccountState> {
  final AccountRepository _accountRepository;

  AccountCubit(this._accountRepository) : super(AccountState());

  Future<void> getAccount() async {
    try {
      final result = await _accountRepository.getAccount();
      AccountData accountData = AccountData.fromJson(result as Map<String, dynamic>);
      emit(state.copyWith(
        firstName: accountData.firstName,
        lastName: accountData.lastName,
        email: accountData.email,
        phoneNumber: accountData.phoneNumber,
        address: accountData.address,
        profilePictureUrl: accountData.avatar,
      ));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> updateAccount(AccountState accountState) async {
    commonCubit.showLoading();
    await _accountRepository.updateAccount(accountState);
    emit(accountState);
    commonCubit.hideLoading();
  }

  Future<void> updateNotifications(bool isNotificationEnabled) async {
    emit(state.copyWith(isNotificationEnabled: isNotificationEnabled));
  }

  Future<void> updateFacebookLogin(bool isFacebookLoginEnabled) async {
    emit(state.copyWith(isFacebookLoginEnabled: isFacebookLoginEnabled));
  }

  Future<void> updateBiometricsLogin(bool isBiometricsEnabled) async {
    emit(state.copyWith(isBiometricsEnabled: isBiometricsEnabled));
  }

  Future<void> getEmailOtp(String email) async {
    try {
      final countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return;
      }
      await _accountRepository.getEmailOtp(email, countryCode);
    } catch (e) {
      handleError(e, (message) => {emit(state.copyWith(errorMessage: message))});
    }
  }

  Future<bool> verifyEmailOtp(String email, String otp) async {
    try {
      final countryCode = await commonCubit.sharedPreferencesService.getCountryCode();
      if (countryCode == null) {
        emit(state.copyWith(errorMessage: "Country code is not selected"));
        return false;
      }
      final result = await _accountRepository.verifyEmailOtp(email, countryCode, otp);
      final authData = AuthTokenInfo.fromJson(result);
      emit(state.copyWith(authTokenInfo: authData));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  Future<void> sendPhoneOtp(String phone) async {}

  Future<void> verifyPhoneOtp(String phone, String otp) async {
    await _accountRepository.verifyPhoneOtp(phone, otp);
    emit(state.copyWith(phoneVerifiedOn: DateTime.now()));
  }

  Future<bool> verifyCurrentPassword(String password) async {
    return await _accountRepository.verifyCurrentPassword(password);
  }

  Future<void> deactivateAccount() async {}

  Future<void> deleteAccount() async {}

  Future<void> getTrafficSources() async {
    try {
      final List<SocialInfo> trafficSources = await _accountRepository.getTrafficSources();
      emit(state.copyWith(trafficSources: trafficSources));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> getInterestedFields() async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      final result = await _accountRepository.getInterestedFields(siteId);
      emit(state.copyWith(interestedFields: result));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> updateInterestedFields(List<PassionateItem> interestedFields) async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      List<int> categoryIds = interestedFields.map((e) => e.id).toList();
      await _accountRepository.upsertInterestedFields(siteId, categoryIds);

      await _clearInterestedFieldsRelatedCaches(siteId);
      emit(state.copyWith(interestedFields: interestedFields));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> sendPaymentOtp() async {
    try {
      await _accountRepository.sendPaymentOtp();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<bool> verifyPaymentOtp(String otp) async {
    try {
      final result = await _accountRepository.verifyPaymentOtp(otp);
      final otpToken = result['otpToken'] as String;
      emit(state.copyWith(paymentOtpToken: otpToken));
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  /// Comprehensive refresh method for pull-to-refresh functionality
  /// This method refreshes account and traffic sources data while bypassing cache to ensure fresh data
  /// Uses isPullToRefresh flag to prevent multiple loading indicators
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/account');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites');

      await Future.wait([
        getAccount(),
        getTrafficSources(),
      ]);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }

  /// Clear cache for interested fields and campaign-related endpoints to ensure data consistency
  /// This ensures that campaign recommendations are updated immediately after interest changes
  Future<void> _clearInterestedFieldsRelatedCaches(int siteId) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/categories');

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/featured-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/top-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/fastest-growing-summary');
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
