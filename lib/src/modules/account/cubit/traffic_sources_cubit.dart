import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/site_cubit.dart';
import 'package:koc_app/src/modules/account/data/repository/account_repository.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/services/url_helpers.dart';
import '../../../shared/utils/handle_error.dart';

class TrafficSourcesCubit extends BaseCubit<SocialInfo> {
  final AccountRepository _accountRepository;
  TrafficSourcesCubit(this._accountRepository) : super(const SocialInfo());

  void emitSocialInfo(SocialInfo socialInfo) {
    emit(socialInfo);
  }

  void updateFollowerNumber(FollowerNumber followerNumber) {
    emit(state.copyWith(totalFollowerLevel: followerNumber));
  }

  void updateId(int id) {
    emit(state.copyWith(id: id));
  }

  void updateSocialType(SocialType socialMediaType) {
    emit(state.copyWith(socialMediaType: socialMediaType));
  }

  Future<void> upsertSocialInfo(SocialInfo socialInfo) async {
    try {
      final type = getSocialTypeFromUrl(socialInfo.url);
      var updatedSource = socialInfo.copyWith(socialMediaType: type);
      if (type == SocialType.OTHER) {
        updatedSource = updatedSource.copyWith(
          totalFollowerLevel: FollowerNumber.EMPTY,
        );
      }
      final siteId = await _accountRepository.updateTrafficSources(updatedSource);

      await _clearSiteRelatedCaches();

      emit(state.copyWith(
        id: siteId,
        socialMediaType: type,
        totalFollowerLevel: updatedSource.totalFollowerLevel,
      ));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<bool> deleteSocialInfo(int id) async {
    try {
      await _accountRepository.deleteTrafficSources(id);

      await _clearSiteRelatedCaches();

      final sites = await commonCubit.sharedPreferencesService.getSites();
      final updatedSites = sites.where((site) => site.id != id).toList();
      await commonCubit.sharedPreferencesService.setSites(updatedSites);
      final siteCubit = Modular.get<SiteCubit>();
      await siteCubit.reloadSites();
      return true;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return false;
    }
  }

  /// Clear cache for site-related endpoints to ensure data consistency
  /// This ensures that site lists and dropdowns are updated immediately after site modifications
  Future<void> _clearSiteRelatedCaches() async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites');
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
