// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AccountStateImpl _$$AccountStateImplFromJson(Map<String, dynamic> json) =>
    _$AccountStateImpl(
      authTokenInfo: json['authTokenInfo'] == null
          ? null
          : AuthTokenInfo.fromJson(
              json['authTokenInfo'] as Map<String, dynamic>),
      userId: (json['userId'] as num?)?.toInt() ?? 0,
      accountType:
          $enumDecodeNullable(_$AccountTypeEnumMap, json['accountType']) ??
              AccountType.INDIVIDUAL,
      trafficSources: (json['trafficSources'] as List<dynamic>?)
              ?.map((e) => SocialInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      interestedFields: (json['interestedFields'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$PassionateItemEnumMap, e))
              .toList() ??
          const [],
      language: json['language'] as String? ?? '',
      isNotificationEnabled: json['isNotificationEnabled'] as bool? ?? false,
      isFacebookLoginEnabled: json['isFacebookLoginEnabled'] as bool? ?? false,
      isBiometricsEnabled: json['isBiometricsEnabled'] as bool? ?? false,
      profilePictureUrl: json['profilePictureUrl'] as String? ?? '',
      firstName: json['firstName'] as String? ?? '',
      lastName: json['lastName'] as String? ?? '',
      email: json['email'] as String? ?? '',
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      phoneNumber: json['phoneNumber'] as String? ?? '',
      phoneVerifiedOn: json['phoneVerifiedOn'] == null
          ? null
          : DateTime.parse(json['phoneVerifiedOn'] as String),
      address: json['address'] as String? ?? '',
      lastPasswordChangedOn: json['lastPasswordChangedOn'] == null
          ? null
          : DateTime.parse(json['lastPasswordChangedOn'] as String),
      companyName: json['companyName'] as String? ?? '',
      companyAddress: json['companyAddress'] as String? ?? '',
      paymentOtpToken: json['paymentOtpToken'] as String? ?? '',
      errorMessage: json['errorMessage'] as String? ?? '',
      isPullToRefresh: json['isPullToRefresh'] as bool? ?? false,
      payment: json['payment'] == null
          ? const AccountPayment()
          : AccountPayment.fromJson(json['payment'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$AccountStateImplToJson(_$AccountStateImpl instance) =>
    <String, dynamic>{
      'authTokenInfo': instance.authTokenInfo,
      'userId': instance.userId,
      'accountType': _$AccountTypeEnumMap[instance.accountType]!,
      'trafficSources': instance.trafficSources,
      'interestedFields': instance.interestedFields
          .map((e) => _$PassionateItemEnumMap[e]!)
          .toList(),
      'language': instance.language,
      'isNotificationEnabled': instance.isNotificationEnabled,
      'isFacebookLoginEnabled': instance.isFacebookLoginEnabled,
      'isBiometricsEnabled': instance.isBiometricsEnabled,
      'profilePictureUrl': instance.profilePictureUrl,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'email': instance.email,
      'isEmailVerified': instance.isEmailVerified,
      'phoneNumber': instance.phoneNumber,
      'phoneVerifiedOn': instance.phoneVerifiedOn?.toIso8601String(),
      'address': instance.address,
      'lastPasswordChangedOn':
          instance.lastPasswordChangedOn?.toIso8601String(),
      'companyName': instance.companyName,
      'companyAddress': instance.companyAddress,
      'paymentOtpToken': instance.paymentOtpToken,
      'errorMessage': instance.errorMessage,
      'isPullToRefresh': instance.isPullToRefresh,
      'payment': instance.payment,
    };

const _$AccountTypeEnumMap = {
  AccountType.INDIVIDUAL: 'INDIVIDUAL',
  AccountType.LOCAL_COMPANY: 'LOCAL_COMPANY',
};

const _$PassionateItemEnumMap = {
  PassionateItem.AUTOMOTIVE: 'AUTOMOTIVE',
  PassionateItem.ENTERTAINMENT: 'ENTERTAINMENT',
  PassionateItem.E_COMMERCE: 'E_COMMERCE',
  PassionateItem.EDUCATION: 'EDUCATION',
  PassionateItem.FINANCIAL: 'FINANCIAL',
  PassionateItem.GAMES: 'GAMES',
  PassionateItem.TELECOMMUNICATION: 'TELECOMMUNICATION',
  PassionateItem.ONLINE_SERVICE: 'ONLINE_SERVICE',
  PassionateItem.TRAVEL_LEISURE: 'TRAVEL_LEISURE',
};
