import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/cubit/common/common_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/traffic_sources_cubit.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';
import 'package:koc_app/src/modules/survey/presentation/widget/social_info_tab_view.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/validator/validators.dart';

class AccountTrafficSourcesUpdatePage extends StatefulWidget {
  final SocialInfo trafficSource;
  const AccountTrafficSourcesUpdatePage(this.trafficSource, {super.key});

  @override
  State<AccountTrafficSourcesUpdatePage> createState() => _AccountTrafficSourcesUpdatePageState();
}

class _AccountTrafficSourcesUpdatePageState extends State<AccountTrafficSourcesUpdatePage> {
  late AccountCubit cubit = Modular.get<AccountCubit>();

  @override
  void initState() {
    super.initState();
    emitSocialInfo(widget.trafficSource);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: CommonAppBar(
          title: Text(widget.trafficSource == const SocialInfo() ? 'Add traffic sources' : widget.trafficSource.name),
          customAction: widget.trafficSource != const SocialInfo()
              ? FutureBuilder<int?>(
                  future: Modular.get<CommonCubit>()
                      .sharedPreferencesService
                      .getCurrentSiteId(),
                  builder: (context, snapshot) {
                    if (!snapshot.hasData) return Container() as Widget;
                    final isLastSource = cubit.state.trafficSources.length <= 1;
                    final isCurrentSite =
                        widget.trafficSource.id == snapshot.data;

                    if (isLastSource || isCurrentSite)
                      return Container() as Widget;

                    return IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () => _showDeleteConfirmation(context),
                    );
                  })
              : null,
        ),
        body: _buildBody());
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            widget.trafficSource.socialMediaType == SocialType.OTHER ? 'Delete Website' : 'Delete Social Media Channel',
            style: Theme.of(context).textTheme.titleMedium!.copyWith(fontWeight: FontWeight.w400, fontSize: 20.r),
          ),
          content: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'Are you sure you want to delete ',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                TextSpan(
                  text: widget.trafficSource.name,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                TextSpan(
                  text: ': \n${widget.trafficSource.url}?',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          actions: <Widget>[
            TextButton(
              style: ButtonStyle(
                overlayColor: WidgetStateProperty.all(Colors.transparent),
              ),
              child: Text(
                'Cancel',
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: Colors.deepOrangeAccent,
                      fontWeight: FontWeight.w400,
                    ),
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepOrangeAccent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteTrafficSource();
              },
              child: Text(
                'Delete',
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w400,
                    ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteTrafficSource() async {
    if (mounted) {
      List<SocialInfo> trafficSources = List.from(cubit.state.trafficSources);
      await ReadContext(context)
          .read<TrafficSourcesCubit>()
          .deleteSocialInfo(widget.trafficSource.id!);
      trafficSources.removeWhere((source) => source.id == widget.trafficSource.id);
      cubit.updateAccount(cubit.state.copyWith(trafficSources: trafficSources));
      Modular.to.pop();
    }
  }

  void emitSocialInfo(SocialInfo socialInfo) {
    ReadContext(context).read<TrafficSourcesCubit>().emitSocialInfo(socialInfo);
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(10.r),
      child: Column(
        spacing: 16.r,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<TrafficSourcesCubit, SocialInfo>(builder: (_, socialInfo) {
            return SocialInfoTabView(
                socialInfo: socialInfo,
                onEmitSocialInfo: emitSocialInfo,
                onFollowerNumber: (item) {
                  ReadContext(context)
                      .read<TrafficSourcesCubit>()
                      .emitSocialInfo(socialInfo.copyWith(totalFollowerLevel: item));
                },
                onClearForm: () {});
          }),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                TextButton(
                    onPressed: () {
                      Modular.to.pop();
                    },
                    child: Text('Cancel',
                        style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFFEF6507)))),
                BlocBuilder<TrafficSourcesCubit, SocialInfo>(builder: (_, socialInfo) {
                  return ElevatedButton(
                      onPressed: isEnableSaveButton(socialInfo)
                          ? () async {
                              if (mounted) {
                                await ReadContext(context)
                                    .read<TrafficSourcesCubit>()
                                    .upsertSocialInfo(socialInfo);
                                List<SocialInfo> trafficSources =
                                    List.from(cubit.state.trafficSources);
                                if (socialInfo.id == null) {
                                  trafficSources.insert(
                                      0,
                                      ReadContext(context)
                                          .read<TrafficSourcesCubit>()
                                          .state);
                                } else {
                                  final data = ReadContext(context)
                                      .read<TrafficSourcesCubit>()
                                      .state;
                                  int index = trafficSources.indexWhere(
                                      (element) => element.id == data.id);
                                  if (index != -1) {
                                    trafficSources[index] = data;
                                  }
                                }
                                cubit.updateAccount(cubit.state
                                    .copyWith(trafficSources: trafficSources));
                                emitSocialInfo(ReadContext(context)
                                    .read<TrafficSourcesCubit>()
                                    .state);
                                Modular.to.pop();
                              }
                            }
                          : null,
                      child: Text(
                        'Save',
                        style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.white),
                      ));
                }),
              ],
            ),
          )
        ],
      ),
    );
  }

  bool isEnableSaveButton(SocialInfo socialInfo) {
    final isNameValid = socialInfo.name.isNotEmpty;
    final isUrlValid =
        socialInfo.url.isNotEmpty && Validators.isValidUrl(socialInfo.url);

    if (!isNameValid || !isUrlValid) return false;

    if (socialInfo.socialMediaType == SocialType.OTHER) {
      return true;
    }

    return Validators.isValidSocialUrl(socialInfo.url);
  }
}
