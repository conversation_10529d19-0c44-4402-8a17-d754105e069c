import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/shared/model/otp_endpoint.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class AccountVerificationPage extends StatefulWidget {
  const AccountVerificationPage({super.key});

  @override
  State<AccountVerificationPage> createState() => _AccountVerificationPageState();
}

class _AccountVerificationPageState extends BasePageState<AccountVerificationPage, AccountCubit> {
  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        title: Text('Payment'),
      ),
      body: _buildBody(),
      bottomSheet: _buildBottomSheet(),
    );
  }

  Widget _buildBottomSheet() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16.r),
      child: ConfirmationButtons(
        btnName: 'Send OTP',
        showCancelButton: true,
        onTap: () async {
          String? email = await cubit.commonCubit.sharedPreferencesService.getEmail();
          Modular.to.pushNamed('/verify-identity/$email', arguments: OtpEndpoint.editPayment);
        },
        isValid: true,
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        spacing: 16.r,
        children: [
          Text(
            'Account verification',
            style: context.textBodyMedium(),
          ),
          Text(
            'To help keep your account and information secure we need to confirm your identity with a One-Time Password (OTP).',
            style: context.textBodySmall(),
            textAlign: TextAlign.center,
          ),
          Text(
            'We will send the verification code to your email address',
            style: context.textBodySmall(),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
