import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_avatar_cubit.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class EditNamePage extends StatefulWidget {
  const EditNamePage({super.key});

  @override
  State<EditNamePage> createState() => _EditNamePageState();
}

class _EditNamePageState extends BasePageState<EditNamePage, AccountSettingsCubit> {
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();

  @override
  void initState() {
    _firstNameController.text = cubit.state.firstName;
    _lastNameController.text = cubit.state.lastName;
    super.initState();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(appBar: const CommonAppBar(title: Text('Name')), body: _buildBody());
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(spacing: 16.r, crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          'This will use as your default contact',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        TextField(
          controller: _firstNameController,
          style: Theme.of(context).textTheme.bodySmall,
          decoration: InputDecoration(
            labelText: 'First name',
            labelStyle: Theme.of(context).textTheme.bodySmall,
            border: const OutlineInputBorder(),
          ),
        ),
        TextField(
          controller: _lastNameController,
          style: Theme.of(context).textTheme.bodySmall,
          decoration: InputDecoration(
            labelText: 'Last name',
            labelStyle: Theme.of(context).textTheme.bodySmall,
            border: const OutlineInputBorder(),
          ),
        ),
        ValueListenableBuilder(
          valueListenable: _firstNameController,
          builder: (context, _, __) {
            return ValueListenableBuilder(
              valueListenable: _lastNameController,
              builder: (context, _, __) {
                return Expanded(
                  child: ConfirmationButtons(
                    btnName: 'Save',
                    showCancelButton: true,
                    onTap: () async {
                      await doLoadingAction(() async {
                        bool result = await cubit.updateName(_firstNameController.text, _lastNameController.text);
                        if (result) {
                          Modular.get<AccountAvatarCubit>()
                              .updateName(_firstNameController.text, _lastNameController.text);
                        }
                        if (context.mounted) {
                          context.showSnackBar(result ? 'Name updated successfully' : 'Failed to update name');
                        }
                        Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
                      });
                    },
                    isValid: _firstNameController.text.isNotEmpty &&
                        (_firstNameController.text != cubit.state.firstName ||
                            _lastNameController.text != cubit.state.lastName),
                  ),
                );
              },
            );
          },
        ),
      ]),
    );
  }
}
