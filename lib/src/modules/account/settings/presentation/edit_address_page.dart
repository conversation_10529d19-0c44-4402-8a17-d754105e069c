import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class EditAddressPage extends StatefulWidget {
  const EditAddressPage({super.key});

  @override
  State<EditAddressPage> createState() => _EditAddressPageState();
}

class _EditAddressPageState extends BasePageState<EditAddressPage, AccountSettingsCubit> {
  final TextEditingController _addressController = TextEditingController();

  @override
  void initState() {
    _addressController.text = cubit.state.address;
    super.initState();
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Address')),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          'This address will use as your default contact',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        SizedBox(height: 16.r),
        SizedBox(
            width: double.infinity,
            child: TextField(
              controller: _addressController,
              maxLines: 3,
              style: Theme.of(context).textTheme.bodySmall,
              decoration: InputDecoration(
                  hintText: 'Address',
                  hintStyle: Theme.of(context).textTheme.bodySmall,
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(6.r),
                    borderSide: const BorderSide(
                      color: Color(0xFFE7E7E7),
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(6.r),
                    borderSide: const BorderSide(color: Color(0x4CEF6507)),
                  )),
            )),
        Text('e.g. 3560 Hoffman Avenue, Sunshine, New York, 10007',
            style: Theme.of(context).textTheme.labelLarge!.copyWith(color: Colors.grey)),
        ValueListenableBuilder(
          valueListenable: _addressController,
          builder: (context, _, __) {
            return Expanded(
              child: ConfirmationButtons(
                btnName: 'Save',
                showCancelButton: true,
                onTap: () async {
                  await doLoadingAction(() async {
                    bool result = await cubit.updateAddress(_addressController.text);
                    if (context.mounted) {
                      context.showSnackBar(result ? 'Address updated successfully' : 'Failed to update address');
                    }
                    if (result) {
                      doLoadingAction(() async {
                        await cubit.findAccount();
                      });
                      Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
                    }
                  });
                },
                isValid: _addressController.text.isNotEmpty && cubit.state.address != _addressController.text,
              ),
            );
          },
        ),
      ]),
    );
  }
}
