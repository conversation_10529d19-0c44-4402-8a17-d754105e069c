import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/shared/cubit/otp_timer_cubit.dart';
import 'package:koc_app/src/modules/shared/cubit/otp_timer_state.dart';

import 'package:koc_app/src/shared/validator/validators.dart';
import 'package:koc_app/src/shared/widgets/common_elevated_button.dart';

class OtpTimerField extends StatefulWidget {
  final String verifyTarget;
  final String btnName;
  final void Function(String value) onTap;
  final Widget? content;
  final VoidCallback? showLoading;
  final VoidCallback? hideLoading;

  const OtpTimerField({
    super.key,
    required this.verifyTarget,
    required this.btnName,
    required this.onTap,
    this.content,
    this.showLoading,
    this.hideLoading,
  });

  @override
  State<OtpTimerField> createState() => _OtpTimerFieldState();
}

class _OtpTimerFieldState extends State<OtpTimerField> {
  final TextEditingController _textEditingController = TextEditingController();
  late final OtpTimerCubit _otpTimerCubit;

  @override
  void initState() {
    super.initState();
    _otpTimerCubit = context.read<OtpTimerCubit>();

    _otpTimerCubit.resetTimerForInitialOtp();
  }

  @override
  void dispose() {
    _otpTimerCubit.dispose();
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                  text: 'Enter the verification code below to verify ', style: Theme.of(context).textTheme.bodySmall),
              TextSpan(
                  text: widget.verifyTarget,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold))
            ],
          ),
        ),
        SizedBox(
          height: 16.r,
        ),
        SizedBox(
          height: 60.r,
          child: TextField(
            controller: _textEditingController,
            keyboardType: TextInputType.number,
            style: Theme.of(context).textTheme.bodySmall,
            onChanged: (value) {
              ReadContext(context).read<OtpTimerCubit>().updateValidation(Validators.isValidOtp(value));
            },
            maxLength: 6,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: const InputDecoration(labelText: 'Verification code'),
          ),
        ),
        SizedBox(
          height: 16.r,
        ),
        BlocBuilder<OtpTimerCubit, OtpTimerState>(
          builder: (context, state) {
            String formattedTime = _formatTime(state.remainingSeconds);
            return Row(
              children: [
                Text(
                  'Didn\'t get the code? ',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                Text(
                  formattedTime,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            );
          },
        ),
        SizedBox(
          height: 16.r,
        ),
        if (widget.content != null) widget.content!,
        const Spacer(),
        Padding(
          padding: EdgeInsets.only(bottom: 16.r),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              BlocBuilder<OtpTimerCubit, OtpTimerState>(
                builder: (context, state) {
                  return GestureDetector(
                    onTap: state.isResendOtp ? null : () => _handleResendCode(context),
                    child: Text(
                      'Resend Code',
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: state.isResendOtp ? Colors.grey : const Color(0xFFEF6507),
                          ),
                    ),
                  );
                },
              ),
              BlocBuilder<OtpTimerCubit, OtpTimerState>(builder: (context, state) {
                return CommonElevatedButton(
                    widget.btnName, state.isValidOtp ? () => widget.onTap(_textEditingController.text) : null);
              }),
            ],
          ),
        ),
      ],
    );
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _handleResendCode(BuildContext context) async {
    _textEditingController.clear();

    final scaffoldMessenger = ScaffoldMessenger.of(context);

    if (widget.showLoading != null) {
      widget.showLoading!();
    }

    try {
      final success = await _otpTimerCubit.resendOtp();

      if (mounted) {
        if (success) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(content: Text('Code resent!')),
          );
        } else {
          final errorMessage = _otpTimerCubit.state.errorMessage.isNotEmpty
              ? _otpTimerCubit.state.errorMessage
              : 'Failed to resend code. Please try again.';
          scaffoldMessenger.showSnackBar(
            SnackBar(content: Text(errorMessage)),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(content: Text('Failed to resend code. Please try again.')),
        );
      }
    } finally {
      if (widget.hideLoading != null) {
        widget.hideLoading!();
      }
    }
  }
}
