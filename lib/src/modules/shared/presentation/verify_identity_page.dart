import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/cubit/account_cubit.dart';
import 'package:koc_app/src/modules/shared/model/otp_endpoint.dart';
import 'package:koc_app/src/modules/shared/widget/otp_timer_field.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'dart:developer' as developer;

class VerifyIdentityPage extends StatefulWidget {
  final String verifyTarget;
  const VerifyIdentityPage({super.key, required this.verifyTarget});

  @override
  State<VerifyIdentityPage> createState() => _VerifyIdentityPageState();
}

class _VerifyIdentityPageState extends BasePageState<VerifyIdentityPage, AccountCubit> {
  late final bool isResetPassword;
  bool _initialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      Object? otpEndpoint = ModalRoute.of(context)?.settings.arguments;
      if (otpEndpoint == OtpEndpoint.editPayment) {
        cubit.sendPaymentOtp();
      } else {
        isResetPassword = ModalRoute.of(context)?.settings.arguments == OtpEndpoint.resetPassword;
        if (isEmail()) {
          cubit.getEmailOtp(widget.verifyTarget);
        } else {
          cubit.sendPhoneOtp(widget.verifyTarget);
        }
      }
      _initialized = true;
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Verify your identity')),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.only(top: 16.r, left: 16.r, right: 16.r),
      child: OtpTimerField(
          verifyTarget: widget.verifyTarget,
          btnName: 'Continue',
          showLoading: () => commonCubit.showLoading(),
          hideLoading: () => commonCubit.hideLoading(),
          onTap: (otp) async {
            commonCubit.showLoading();
            Object? otpEndpoint = ModalRoute.of(context)?.settings.arguments;
            if (otpEndpoint == OtpEndpoint.editPayment) {
              final isVerified = await cubit.verifyPaymentOtp(otp);
              if (!isVerified) {
                context.showSnackBar(cubit.state.errorMessage);
              } else {
                Modular.to.pushNamed((otpEndpoint as OtpEndpoint).path);
              }
            } else if (isEmail()) {
              final result = await cubit.verifyEmailOtp(widget.verifyTarget, otp);
              if (mounted) {
                if (result) {
                  Object? otpEndpoint = ModalRoute.of(context)?.settings.arguments;
                  if (otpEndpoint == null) {
                    Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
                  } else {
                    Modular.to.pushNamed((otpEndpoint as OtpEndpoint).path);
                  }
                } else {
                  context.showSnackBar(cubit.state.errorMessage);
                }
              }
            } else {
              await cubit.verifyPhoneOtp(widget.verifyTarget, otp);
              if (mounted) {
                if (cubit.state.phoneVerifiedOn != null) {
                  cubit.updateAccount(cubit.state.copyWith(phoneNumber: widget.verifyTarget));
                  Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
                } else {
                  context.showSnackBar(cubit.state.errorMessage);
                }
              }
            }
            commonCubit.hideLoading();
          }),
    );
  }

  bool isEmail() {
    return widget.verifyTarget.contains('@');
  }
}
