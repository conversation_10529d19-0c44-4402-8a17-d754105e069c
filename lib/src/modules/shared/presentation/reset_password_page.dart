import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/account/presentation/widget/confirmation_buttons.dart';
import 'package:koc_app/src/modules/account/settings/cubit/account_settings_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/reset_password_cubit.dart';
import 'package:koc_app/src/modules/account/settings/cubit/reset_password_state.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';

class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key});

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends BasePageState<ResetPasswordPage, AccountSettingsCubit> {
  late ResetPasswordCubit resetPasswordCubit = Modular.get<ResetPasswordCubit>();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    String currentPassword = Modular.args.data ?? '';
    return Scaffold(
      appBar: const CommonAppBar(title: Text('Reset password')),
      body: _buildBody(),
      bottomSheet: Container(
        color: Colors.white,
        padding: EdgeInsets.all(16.r),
        child: BlocBuilder<ResetPasswordCubit, ResetPasswordState>(
            bloc: resetPasswordCubit,
            builder: (context, state) {
              return ConfirmationButtons(
                  btnName: 'Set password',
                  isValid: state.isValid,
                  onTap: () async {
                    commonCubit.showLoading();
                    final isAccountSetting =
                        Modular.to.navigateHistory.where((e) => e.uri.path.contains('/account/settings/')).isNotEmpty;
                    bool result = false;
                    if (currentPassword.isNotEmpty) {
                      result = await cubit.changePassword(currentPassword, _confirmPasswordController.text);
                    } else {
                      result = await cubit.resetPassword(_confirmPasswordController.text);
                    }
                    if (result) {
                      context.showSnackBar('Password has been reset');
                      if (isAccountSetting) {
                        Modular.to.popUntil(ModalRoute.withName('/account/settings/'));
                      } else {
                        Modular.to.popUntil(ModalRoute.withName('/sign-in-by-password/'));
                      }
                    } else {
                      context.showSnackBar('Failed to reset password');
                    }
                    commonCubit.hideLoading();
                  });
            }),
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Set a new password for your account',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          SizedBox(
            height: 16.r,
          ),
          BlocBuilder<ResetPasswordCubit, ResetPasswordState>(
              bloc: resetPasswordCubit,
              builder: (context, state) {
                return TextField(
                  controller: _passwordController,
                  obscureText: state.obscurePassword,
                  onChanged: (value) {
                    resetPasswordCubit.setValid(isPasswordValid());
                  },
                  style: Theme.of(context).textTheme.bodySmall,
                  decoration: InputDecoration(
                    labelText: 'New password',
                    labelStyle: Theme.of(context).textTheme.bodySmall,
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        state.obscurePassword ? Icons.visibility_off : Icons.visibility,
                      ),
                      onPressed: () {
                        resetPasswordCubit.toggleObscurePassword();
                      },
                    ),
                  ),
                );
              }),
          SizedBox(
            height: 8.r,
          ),
          Text(
            'Use 8 or more characters with a mix of letters, numbers & symbols.',
            style: Theme.of(context).textTheme.labelLarge!.copyWith(color: const Color(0xFF767676)),
          ),
          SizedBox(height: 16.r),
          BlocBuilder<ResetPasswordCubit, ResetPasswordState>(
              bloc: resetPasswordCubit,
              builder: (context, state) {
                return TextField(
                  controller: _confirmPasswordController,
                  obscureText: state.obscureConfirmPassword,
                  onChanged: (value) {
                    resetPasswordCubit.setValid(isPasswordValid());
                  },
                  style: Theme.of(context).textTheme.bodySmall,
                  decoration: InputDecoration(
                    labelText: 'Confirm new password',
                    labelStyle: Theme.of(context).textTheme.bodySmall,
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        state.obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
                      ),
                      onPressed: () {
                        resetPasswordCubit.toggleObscureConfirmPassword();
                      },
                    ),
                  ),
                );
              }),
        ],
      ),
    );
  }

  bool isPasswordValid() {
    final passwordRegex = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,}$');
    return _passwordController.text == _confirmPasswordController.text &&
        passwordRegex.hasMatch(_confirmPasswordController.text);
  }
}
