import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/base/cubit/base_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_state.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/data/repository/voucher_repository.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/utils/handle_error.dart';

class VoucherCubit extends BaseCubit<VoucherState> {
  final VoucherRepository voucherRepository;

  VoucherCubit(this.voucherRepository) : super(VoucherState());

  Future<void> findVouchers(FindVouchersRequest request) async {
    try {
      if (request.campaignId != null || request.category != null) {
        emit(state.copyWith(loadingCampaignVouchers: true));
      } else {
        emit(state.copyWith(isDataLoaded: false));
      }

      final vouchers = await voucherRepository.findVouchers(request);
      _processVouchers(request, vouchers);

      if (request.campaignId != null || request.category != null) {
        emit(state.copyWith(loadingCampaignVouchers: false));
      } else {
        emit(state.copyWith(isDataLoaded: true));
      }
    } catch (e) {
      handleError(e, (message) {
        if (request.campaignId != null || request.category != null) {
          emit(state.copyWith(loadingCampaignVouchers: false, errorMessage: message));
        } else {
          emit(state.copyWith(isDataLoaded: true, errorMessage: message));
        }
      });
    }
  }

  void _processVouchers(FindVouchersRequest request, List<Voucher> vouchers) {
    if (request.category != null) {
      final categoryType = VoucherCategoryType.fromCategoryId(request.category!);
      if (categoryType != null) {
        switch (categoryType) {
          case VoucherCategoryType.travelHotel:
            emit(state.copyWith(travelAndHotels: vouchers));
            break;
          case VoucherCategoryType.electronics:
            emit(state.copyWith(electronics: vouchers));
            break;
          case VoucherCategoryType.fashion:
            emit(state.copyWith(fashion: vouchers));
            break;
          case VoucherCategoryType.beautyHealth:
            emit(state.copyWith(beautyAndHealth: vouchers));
            break;
          case VoucherCategoryType.homeLiving:
            emit(state.copyWith(homeAndLiving: vouchers));
            break;
          case VoucherCategoryType.foodGrocery:
            emit(state.copyWith(foodAndGrocery: vouchers));
            break;
        }
      }
    } else if (request.campaignId != null) {
      emit(state.copyWith(campaignVouchers: vouchers));
    } else {
      emit(state.copyWith(allVouchers: vouchers));
    }
  }

  Future<void> getVoucherCategories() async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      emit(state.copyWith(loadingCategories: true));

      List<VoucherCategory> categories = await voucherRepository.getVoucherCategories(siteId, 'ALL');
      emit(state.copyWith(categories: categories, loadingCategories: false));
    } catch (e) {
      handleError(e, (message) {
        emit(state.copyWith(categories: [], errorMessage: message, loadingCategories: false));
      });
    }
  }

  Future<Voucher?> findVoucherById(int voucherId) async {
    try {
      final siteId = (await commonCubit.sharedPreferencesService.getCurrentSiteId())!;
      emit(state.copyWith(loadingVoucherDetail: true));

      final detailedVoucher = await voucherRepository.findVoucherById(voucherId, siteId);

      if (detailedVoucher != null) {
        emit(state.copyWith(currentVoucherDetail: detailedVoucher, loadingVoucherDetail: false));
      } else {
        emit(state.copyWith(loadingVoucherDetail: false));
      }

      return detailedVoucher;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message, loadingVoucherDetail: false)));
      return null;
    }
  }

  Future<List<Voucher>> fetchVoucherByCategory(String categoryId) async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      FindVouchersRequest request = FindVouchersRequest(
        siteId: siteId!,
        startIndex: 1,
        size: 100,
      );
      if (categoryId.isNotEmpty) {
        request = request.copyWith(category: categoryId);
      }
      final vouchers = await voucherRepository.findVouchers(request);
      return vouchers;
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
      return [];
    }
  }

  void changeIndex(int tabIndex) {
    emit(state.copyWith(tabIndex: tabIndex));
  }

  List<Voucher> getCurrentCampaignVouchers() {
    return state.campaignVouchers;
  }

  void updateVouchers(List<Voucher> newVouchers) {
    emit(state.copyWith(allVouchers: newVouchers));
  }

  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));
      await _refreshVoucherDataCore();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }

  Future<void> _refreshVoucherDataCore() async {
    final apiService = Modular.get<ApiService>();
    final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

    if (siteId != null) {
      await _clearVoucherCaches(apiService);
    }

    await Future.wait([
      _refreshVouchersForPullToRefresh(),
      _refreshVoucherCategoriesForPullToRefresh(),
    ]);

    if (state.tabIndex > 0) {
      await _refreshCurrentTabForPullToRefresh();
    }
  }

  Future<void> _clearVoucherCaches(ApiService apiService) async {
    await apiService.clearCacheForEndpoint('/v3/publishers/me/vouchers');
    await apiService.clearCacheForEndpoint('/v3/publishers/me/voucher-categories');
    await apiService.clearCacheForEndpoint('/v3/publishers/me/campaigns/vouchers-available/names');
  }

  Future<void> _refreshVouchersForPullToRefresh() async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) return;

      final request = FindVouchersRequest(siteId: siteId, type: 'ALL');
      final vouchers = await voucherRepository.findVouchers(request);

      emit(state.copyWith(allVouchers: vouchers));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> _refreshVoucherCategoriesForPullToRefresh() async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) return;

      final categories = await voucherRepository.getVoucherCategories(siteId, 'ALL');

      emit(state.copyWith(categories: categories));
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  /// Specialized refresh method for current tab during pull-to-refresh
  /// This method doesn't trigger loading states to avoid multiple loading indicators
  Future<void> _refreshCurrentTabForPullToRefresh() async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) return;

      String? categoryId;
      switch (state.tabIndex) {
        case 1:
          categoryId = VoucherCategoryType.travelHotel.categoryId;
          break;
        case 2:
          categoryId = VoucherCategoryType.electronics.categoryId;
          break;
        case 3:
          categoryId = VoucherCategoryType.fashion.categoryId;
          break;
        case 4:
          categoryId = VoucherCategoryType.beautyHealth.categoryId;
          break;
        case 5:
          categoryId = VoucherCategoryType.homeLiving.categoryId;
          break;
        case 6:
          categoryId = VoucherCategoryType.foodGrocery.categoryId;
          break;
      }

      if (categoryId != null) {
        final request = FindVouchersRequest(siteId: siteId, category: categoryId);
        final vouchers = await voucherRepository.findVouchers(request);

        _processVouchers(request, vouchers);
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }

  Future<void> refreshVoucherDataAfterSiteSwitch() async {
    try {
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();
      if (siteId == null) return;

      await _refreshVoucherDataCore();

      emit(state.copyWith(isDataLoaded: true, loadingCampaignVouchers: false));
    } catch (e) {
      handleError(e,
          (message) => emit(state.copyWith(errorMessage: message, isDataLoaded: true, loadingCampaignVouchers: false)));
    }
  }
}
