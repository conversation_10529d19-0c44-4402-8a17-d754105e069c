import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/base/base_page_state.dart';
import 'package:koc_app/src/modules/campaign/cubit/campaign_detail_cubit.dart';
import 'package:koc_app/src/modules/campaign/cubit/creative_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_cubit.dart';
import 'package:koc_app/src/modules/campaign/custom_link/cubit/custom_link_history_cubit.dart';
import 'package:koc_app/src/modules/campaign/data/models/campaign.dart';
import 'package:koc_app/src/modules/campaign/presentation/widget/campaign_summary_horizontal_view.dart';
import 'package:koc_app/src/modules/home/<USER>/home_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/home_state.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/widget/voucher_card.dart';
import 'package:koc_app/src/modules/navigation/bottom_navigation_cubit.dart';
import 'package:koc_app/src/shared/constants/color_constants.dart';
import 'package:koc_app/src/shared/data/image_data.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/mixin/common_mixin.dart';
import 'package:koc_app/src/shared/widgets/common_app_bar.dart';
import 'package:koc_app/src/shared/widgets/common_image_slider.dart';
import 'package:koc_app/src/shared/widgets/pull_to_refresh_wrapper.dart';
import 'package:url_launcher/url_launcher_string.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends BasePageState<HomePage, HomeCubit> with CommonMixin {
  @override
  void initState() {
    doLoadingAction(() async {
      await cubit.initState();
    });
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        logo: Padding(
          padding: EdgeInsets.only(left: 16.r),
          child: buildLogo(),
        ),
        showNotificationAction: true,
      ),
      body: BlocBuilder<HomeCubit, HomeState>(
        bloc: cubit,
        builder: (context, state) {
          return _buildBody(state);
        },
      ),
    );
  }

  Widget _buildBody(HomeState state) {
    return PullToRefreshWrapper(
      onRefresh: () => cubit.pullToRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(bottom: 8.r),
          child: Column(
            spacing: 22.r,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonImageSlider(state.homeCarousel.map((item) => ImageData(imageUrl: item.image)).toList()),
              Padding(
                padding: EdgeInsets.only(left: 16.r, right: 16.r),
                child: Column(
                  spacing: 22.r,
                  children: [
                    _buildPerformance(state),
                    _buildCampaigns(state.promotedCampaigns, cubit.currency),
                    _buildVoucherCode(state.vouchers),
                    _buildTools(state),
                  ],
                ),
              )

              // _buildSuperPoint(state.superPoints)
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTools(HomeState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Tools', null),
        SizedBox(height: 14.r),
        _buildToolCard(
          'Academy',
          'Learn everything you need to know about affiliate marketing.',
          'Learn now',
          const Color(0xFF1F78D1),
          state.academyLink,
          Icons.school_outlined,
          const Color(0xFFE4F2FF),
        ),
        SizedBox(height: 8.r),
        _buildToolCard(
          'Inpages',
          'Showcase your profile, links, products easily, all in one page!',
          'Try now',
          const Color(0xFFEF8A13),
          state.inpageLink,
          Icons.newspaper_outlined,
          const Color(0xFFFFB522).withValues(alpha: 0.3),
        ),
        SizedBox(height: 8.r),
        _buildToolCard(
          'Super point',
          'You can use it to exchange with our original gifts.',
          'Try now',
          const Color(0xFFEF6507),
          state.superPointLink,
          Icons.stars_outlined,
          const Color(0xFFEF6507).withValues(alpha: 0.3),
        ),
      ],
    );
  }

  Widget _buildToolCard(String title, String description, String buttonName, Color buttonColor, String link,
      IconData icon, Color cardColor) {
    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      padding: EdgeInsets.all(12.r),
      child: Column(
        spacing: 12.r,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: context.textBodySmall(fontWeight: FontWeight.w500),
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  description,
                  style: context.textLabelLarge(color: ColorConstants.textColor),
                ),
              ),
              Icon(icon, size: 40.r, color: buttonColor),
            ],
          ),
          GestureDetector(
            onTap: () async {
              try {
                await launchUrlString(link, mode: LaunchMode.inAppBrowserView);
              } catch (e) {
                // do nothing.
              }
            },
            child: Row(
              spacing: 4.r,
              children: [
                Text(buttonName, style: context.textLabelLarge(fontWeight: FontWeight.w500, color: buttonColor)),
                Icon(Icons.arrow_forward, size: 20.r, color: buttonColor)
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildVoucherCode(List<Voucher> vouchers) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildTitle('Voucher code', () {
          Modular.to.pushNamed('/home/<USER>/');
        }),
        SizedBox(height: 14.r),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            spacing: 8.r,
            children: [
              for (final voucher in vouchers.take(5))
                VoucherCard(
                  voucher,
                  width: ScreenUtil().screenWidth - 50.r,
                ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildCampaigns(List<DefaultCampaignSummary> campaigns, String currency) {
    return MultiBlocProvider(
        providers: [
          BlocProvider.value(value: Modular.get<CampaignDetailCubit>()),
          BlocProvider.value(value: Modular.get<CreativeCubit>()),
          BlocProvider.value(value: Modular.get<VoucherCubit>()),
          BlocProvider.value(value: Modular.get<CustomLinkCubit>()),
          BlocProvider.value(value: Modular.get<CustomLinkHistoryCubit>()),
        ],
        child: CampaignSummaryHorizontalView('Campaigns', currency, () {
          Modular.get<BottomNavigationCubit>().navigateTo(1);
        }, campaigns));
  }

  Widget _buildPerformance(HomeState state) {
    Color performanceTileColor = Colors.grey[300]!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Performance',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ],
        ),
        SizedBox(height: 14.r),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(color: performanceTileColor, width: 1.r),
          ),
          child: Column(
            children: [
              IntrinsicHeight(
                child: Row(
                  children: [
                    _buildPerformanceTile(
                        state.lastSevenDayPerformance?.earnings.current ?? 0,
                        state.lastSevenDayPerformance?.earnings.changedValue ?? 0,
                        state.lastSevenDayPerformance?.earnings.changedPercentage ?? 0,
                        cubit.currency,
                        'Earnings',
                        'Earning Occurred'),
                    VerticalDivider(
                      color: performanceTileColor,
                      width: 1.r,
                    ),
                    _buildPerformanceTile(
                        state.lastSevenDayPerformance?.clicks.current ?? 0,
                        state.lastSevenDayPerformance?.clicks.changedValue ?? 0,
                        state.lastSevenDayPerformance?.clicks.changedPercentage ?? 0,
                        '',
                        'Clicks',
                        'Click'),
                  ],
                ),
              ),
              Divider(
                color: performanceTileColor,
                height: 1.r,
              ),
              IntrinsicHeight(
                child: Row(
                  children: [
                    _buildPerformanceTile(
                        state.lastSevenDayPerformance?.conversions.current ?? 0,
                        state.lastSevenDayPerformance?.conversions.changedValue ?? 0,
                        state.lastSevenDayPerformance?.conversions.changedPercentage ?? 0,
                        '',
                        'Conversions',
                        'Conversion'),
                    VerticalDivider(
                      color: performanceTileColor,
                      width: 1.r,
                    ),
                    _buildPerformanceTile(
                        state.lastSevenDayPerformance?.earningsPerClick.current ?? 0,
                        state.lastSevenDayPerformance?.earningsPerClick.changedValue ?? 0,
                        state.lastSevenDayPerformance?.earningsPerClick.changedPercentage ?? 0,
                        cubit.currency,
                        'EPC',
                        'EPC Occurred'),
                  ],
                ),
              ),
              Divider(
                color: performanceTileColor,
                height: 1.r,
              ),
              if (state.showChart) _buildChart(state),
              if (state.showChart)
                Divider(
                  color: performanceTileColor,
                  height: 1.r,
                ),
              IconButton(
                onPressed: () {
                  cubit.toggleChart();
                },
                icon: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${state.showChart ? 'Hide' : 'View'} chart',
                      style: context.textLabelLarge(
                        color: const Color(0xFFEF6507),
                      ),
                    ),
                    Icon(
                        color: const Color(0xFFEF6507),
                        state.showChart ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                        size: 24.r),
                  ],
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  Widget _buildChart(HomeState state) {
    final bool hasData = state.clicks.any((c) => c > 0) || state.conversions.any((c) => c > 0);

    if (!hasData) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: 24.r),
        child: Center(
          child: Column(
            children: [
              Icon(Icons.bar_chart_outlined, size: 48.r, color: Colors.grey[400]),
              SizedBox(height: 8.r),
              Text(
                'No data available',
                style: context.textLabelMedium(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    final List<String> days = List.generate(7, (index) {
      DateTime date = DateTime.now().subtract(Duration(days: 6 - index));
      return DateFormat('MMM d').format(date);
    });

    final maxY = getRoundUpValue(getMaxY(state.clicks, state.conversions));

    const clicksColor = Color(0xFF4CAF50); // Colors.green[600]
    const conversionsColor = Color(0xFFFFB300); // Colors.yellow[600]

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.all(8.0.r),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildChartLegend('Clicks', clicksColor),
              SizedBox(width: 32.r),
              _buildChartLegend('Conversions', conversionsColor),
            ],
          ),
        ),
        AspectRatio(
          aspectRatio: 1.5,
          child: Padding(
            padding: EdgeInsets.only(top: 8.0.r, left: 16.r, right: 16.r),
            child: LineChart(
              LineChartData(
                titlesData: _buildChartTitles(context, days, maxY.toInt()),
                gridData: const FlGridData(show: false),
                borderData: FlBorderData(show: false),
                minX: 0,
                maxX: 6,
                minY: 0,
                maxY: maxY.toDouble(),
                lineBarsData: [
                  _buildLineChartData(state.clicks, clicksColor),
                  _buildLineChartData(state.conversions, conversionsColor),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChartLegend(String label, Color color) {
    return Row(
      children: [
        CircleAvatar(
          backgroundColor: color,
          radius: 5.r,
        ),
        SizedBox(width: 12.r),
        Text(
          label,
          style: Theme.of(context).textTheme.labelMedium,
        ),
      ],
    );
  }

  FlTitlesData _buildChartTitles(BuildContext context, List<String> days, int maxY) {
    int maxYLength = maxY.toString().length;
    return FlTitlesData(
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 20.r,
          interval: 1,
          getTitlesWidget: (value, meta) {
            int index = value.toInt();
            return index >= 0 && index < days.length
                ? Text(
                    days[index],
                    style: Theme.of(context).textTheme.labelSmall,
                    textAlign: TextAlign.center,
                  )
                : Container();
          },
        ),
      ),
      leftTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 20.r + (6.r * maxYLength),
          getTitlesWidget: (value, meta) {
            return Text(
              value.toInt().toString(),
              style: Theme.of(context).textTheme.labelSmall,
            );
          },
        ),
      ),
      topTitles: const AxisTitles(
        sideTitles: SideTitles(showTitles: false),
      ),
      rightTitles: const AxisTitles(
        sideTitles: SideTitles(showTitles: false),
      ),
    );
  }

  LineChartBarData _buildLineChartData(List<num> data, Color color) {
    return LineChartBarData(
      spots: List.generate(
        7,
        (index) => FlSpot(index.toDouble(), data[index].toDouble()),
      ),
      isCurved: true,
      preventCurveOverShooting: true,
      color: color,
      dotData: const FlDotData(show: true),
      belowBarData: BarAreaData(show: false),
    );
  }

  Widget _buildPerformanceTile(
      num current, num changeValue, num changedPercentage, String currency, String label, String description) {
    bool isIncreased = changeValue > 0;
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(8.0.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              current.toPrice(currency),
              style: context.textBodySmall(fontSize: 16.r, fontWeight: FontWeight.bold),
            ),
            if (changeValue != 0)
              Row(
                children: [
                  Icon(isIncreased ? Icons.arrow_upward : Icons.arrow_downward,
                      color: isIncreased ? Colors.green : Colors.red, size: 12.r),
                  Text(
                    '${changeValue.toCommaSeparated()} (${changedPercentage.toPercentageChanged()})',
                    style: context.textLabelMedium(
                      fontSize: 12.r,
                      color: const Color(0xFF767676),
                    ),
                  ),
                ],
              ),
            Row(
              children: [
                Text(
                  label,
                  style: context.textLabelLarge(fontWeight: FontWeight.w500, color: const Color(0xFF767676)),
                ),
                GestureDetector(
                  onTap: () {
                    showDescription(context, label, description);
                  },
                  child: Icon(Icons.help_outline, size: 16.r, color: const Color(0xFF767676)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
