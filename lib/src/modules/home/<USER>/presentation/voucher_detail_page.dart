import 'package:koc_app/src/shared/widgets/cached_image_with_placeholder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_cubit.dart';
import 'package:koc_app/src/modules/home/<USER>/cubit/voucher_state.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/shared/extensions.dart';
import 'package:koc_app/src/shared/widgets/common_loading.dart';

class VoucherDetailPage extends StatefulWidget {
  final Voucher voucher;
  final int? voucherId;

  const VoucherDetailPage({required this.voucher, this.voucherId, super.key});

  @override
  State<VoucherDetailPage> createState() => _VoucherDetailPageState();
}

class _VoucherDetailPageState extends State<VoucherDetailPage> {
  late VoucherCubit _voucherCubit;

  @override
  void initState() {
    super.initState();
    _voucherCubit = Modular.get<VoucherCubit>();
    _loadVoucherDetails();
  }

  Future<void> _loadVoucherDetails() async {
    if (widget.voucherId != null) {
      await _voucherCubit.findVoucherById(widget.voucherId!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 12.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: BlocBuilder<VoucherCubit, VoucherState>(
          bloc: _voucherCubit,
          builder: (context, state) {
            final Voucher displayVoucher = state.currentVoucherDetail ?? widget.voucher;

            return Column(
              children: [
                _buildHeader(displayVoucher),
                if (state.loadingVoucherDetail)
                  const Expanded(
                    child: Center(
                      child: CommonLoading(),
                    ),
                  )
                else ...[
                  _buildImage(displayVoucher),
                  _buildContents(displayVoucher),
                ],
              ],
            );
          }),
    );
  }

  Widget _buildContents(Voucher voucher) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCard(voucher),
              SizedBox(height: 16.r),
              _buildDescription(voucher),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDescription(Voucher voucher) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          voucher.description.isNotEmpty ? voucher.description : '-',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildCard(Voucher voucher) {
    String categoryName = voucher.campaignName;

    if (voucher.categoryIds.isNotEmpty) {
      List<String> categoryNames = [];

      for (String categoryId in voucher.categoryIds) {
        final categoryType = VoucherCategoryType.fromCategoryId(categoryId);
        if (categoryType != null) {
          categoryNames.add(categoryType.displayName);
        }
      }

      if (categoryNames.isNotEmpty) {
        categoryName = categoryNames.join(", ");
      }
    }

    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r), border: Border.all(color: const Color(0xFFE7E7E7), width: 1.r)),
      child: Column(
        spacing: 8.r,
        children: [
          _buildCardContent('Promote code', voucher.code),
          _buildCardContent('Expired in', DateTime.parse(voucher.validDate).toDateMonthYear(),
              valueColor: const Color(0xFFA72D1A)),
          _buildCardContent('Category', categoryName),
        ],
      ),
    );
  }

  Widget _buildCardContent(String title, String value, {Color valueColor = const Color(0xFF464646)}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Flexible(
          flex: 2,
          child: Text(title, style: Theme.of(context).textTheme.labelLarge!.copyWith(fontWeight: FontWeight.w500)),
        ),
        SizedBox(width: 8.r),
        Flexible(
          flex: 3,
          child: Text(
            value,
            style: Theme.of(context).textTheme.labelLarge!.copyWith(
                  fontWeight: FontWeight.w500,
                  color: valueColor,
                ),
            textAlign: TextAlign.right,
            softWrap: true,
            overflow: TextOverflow.ellipsis,
            maxLines: 3,
          ),
        ),
      ],
    );
  }

  Widget _buildImage(Voucher voucher) {
    return SizedBox(
      width: double.infinity,
      child: CachedImageWithPlaceholder(
        imageUrl: voucher.imageUrl,
        height: 120.r,
        errorWidget: Container(
          height: 120.r,
          color: Colors.grey[300],
          child: Center(
            child: Icon(Icons.error, color: Colors.grey[500]),
          ),
        ),
        placeholder: Container(
          height: 120.r,
          color: Colors.grey[200],
          child: Center(
            child: CircularProgressIndicator(strokeWidth: 2.r),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(Voucher voucher) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 12.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              voucher.title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ),
          SizedBox(width: 16.r),
          Row(
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: CircleAvatar(
                  backgroundColor: Colors.grey[200],
                  radius: 12.r,
                  child: Icon(Icons.close, size: 16.r),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
