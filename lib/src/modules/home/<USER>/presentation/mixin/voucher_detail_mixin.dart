import 'package:flutter/material.dart';
import 'package:koc_app/src/modules/home/<USER>/data/model/voucher.dart';
import 'package:koc_app/src/modules/home/<USER>/presentation/voucher_detail_page.dart';

mixin VoucherDetailMixin {
  void showVoucherDetailPage(BuildContext context, Voucher voucher) {
    showModalBottomSheet(
      isScrollControlled: true,
      useSafeArea: true,
      context: context,
      builder: (BuildContext context) {
        return VoucherDetailPage(
          voucher: voucher,
          voucherId: voucher.id,
        );
      },
    );
  }
}
