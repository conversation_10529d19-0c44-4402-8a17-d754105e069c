name: koc_app
description: "A new Flutter project."

# Prevent accidental publishing to pub.dev.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.5.3

dependencies:
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  convex_bottom_bar: ^3.2.0
  csv: ^6.0.0
  custom_date_range_picker: ^1.1.0
  dio: ^5.7.0
  dotted_border: ^2.1.0
  easy_localization: ^3.0.7
  equatable: ^2.0.5
  fl_chart: ^0.70.0
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  flutter_cache_manager: ^3.4.1
  flutter_dotenv: ^5.2.1
  flutter_facebook_auth: ^6.2.0
  app_tracking_transparency: ^2.0.6+1
  flutter_keyboard_visibility: ^6.0.0
  flutter_modular: ^6.3.4
  flutter_screenutil: ^5.9.3
  flutter_secure_storage: ^9.2.2
  flutter_svg: ^2.0.16
  flutter_widget_from_html: ^0.16.0
  flutter_form_builder: ^10.0.1
  google_sign_in: ^6.2.2
  image_picker: ^1.1.2
  infinite_scroll_pagination: ^4.1.0
  lazy_load_indexed_stack: ^1.1.0
  local_auth: ^2.3.0
  material_symbols_icons: ^4.2719.3
  path_provider: ^2.1.5
  share_plus: ^10.1.2
  shared_preferences: ^2.5.3
  url_launcher: ^6.3.1
  visibility_detector: ^0.4.0+2
  synchronized: ^3.3.1
  graphic: ^2.5.1
  video_player_android: 2.8.2
  flutter_image_compress: ^2.4.0
  flutter_native_splash: ^2.4.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  device_info_plus: ^11.4.0
  connectivity_plus: ^6.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0
  bloc_test: ^9.1.7
  build_runner: ^2.4.13
  freezed: ^2.5.7
  json_serializable: ^6.8.0
  mockito: ^5.4.4
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  logger: ^2.0.2+1
  hive_generator: ^2.0.1
  collection: ^1.18.0
  http: ^1.2.2

flutter:
  uses-material-design: true

  # Enable generation of localized Strings from arb files.
  generate: true

  assets:
    # Add assets from the images directory to the application.
    - .env
    - assets/images/
    - assets/images/socials/
    - assets/translations/

  fonts:
    - family: Metropolis
      fonts:
        - asset: assets/fonts/Metropolis-Regular.otf
          weight: 400
        - asset: assets/fonts/Metropolis-Medium.otf
          weight: 500
        - asset: assets/fonts/Metropolis-SemiBold.otf
          weight: 600
        - asset: assets/fonts/Metropolis-Bold.otf
          weight: 700
